using Client.Common.Abilities.Infrastructure.Data;
using Client.Common.Abilities.Meta.Data;
using Client.Common.Abilities.Meta.Events;
using Client.Common.BloodColba.Data;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Utils.GameLogger;
using Client.Utils.ResultTool.Results;
using Common;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;

namespace Client.Common.Abilities.Meta.Systems
{
    public class BloodColbaMaxSizeAbilityRemoveSystem : IEcsInitSystem, IEcsRunSystem
    {
        private readonly MetaNet _metaNet = default;
        private readonly EcsFilter<BloodColbaAdsDeactivateEvent> _bloodColbaAdsDeactivateEvent = default;
        private readonly EcsFilter<BloodColbaAdsAnim> _bloodColbaAdsAnim = default;

        private readonly PlayerManager _playerManager = default;
        private MetaAbilities MetaAbilities => _playerManager.Session.MetaAbilities;
        
        public void Init()
        {
            if (_playerManager.Session.BloodColba.Blood > 0)
            {
                return;
            }
            
            RemoveAbility().Forget();
        }

        public void Run()
        {
            if (_bloodColbaAdsDeactivateEvent.IsEmpty())
            {
                return;
            }
            
            _bloodColbaAdsDeactivateEvent.GetEntity(0).Destroy();
            RemoveAbility().Forget();
        }

        private async UniTask RemoveAbility()
        {
            await MetaAbilities.RemoveAbility((int)AbilityId.ChangeBloodColbaMaxSizeByAds_44017);
            await RemoveAdsItem();
            ClearAnimationEvent();
        }

        private void ClearAnimationEvent()
        {
            foreach (int idx in _bloodColbaAdsAnim)
            {
                _bloodColbaAdsAnim.GetEntity(idx).Del<BloodColbaAdsAnim>();
            }
        }
        
        private async UniTask RemoveAdsItem()
        {
            if (!_playerManager.Session.Inventory.Contains(PlayerInventory.Items.BLOOD_COLBA_ADS))
            {
                return;
            }
            
            ItemComponent item = new()
            {
                Id = PlayerInventory.Items.BLOOD_COLBA_ADS,
                Count = 1
            };
            
            Result removeItemFromInventory = await _metaNet.RemoveItemFromInventory(item);
            if (removeItemFromInventory.IsFailure)
            {
                GameLogger.LogWarning($"[{nameof(RemoveAdsItem)}] Remove ads item error: {removeItemFromInventory.Error}");
            }
        }
    }
}