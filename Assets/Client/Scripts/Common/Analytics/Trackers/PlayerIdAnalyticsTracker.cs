using System.Collections.Generic;
using Client.Common.Analytics.Infrastructure;
using Client.Common.Analytics.Infrastructure.Events;
using Client.Common.Analytics.Trackers.Abstract;
using Client.Common.Network.Authentication.Credentials.Social.Facebook;
using Client.Common.Network.Authentication.Credentials.Social.Google;
using Client.Common.Player.Controllers;
using Leopotam.Ecs;

namespace Client.Common.Analytics.Trackers
{
    public class PlayerIdAnalyticsTracker : IAnalyticTracker
    {
        private readonly EcsWorld _world;
        private readonly PlayerManager _playerManager;
        private readonly FacebookAuthService _facebook;
        private readonly GoogleAuthService _google;

        private readonly EcsFilter<AnalyticsEventApplied<PlayerIdAnalyticsTracker>> _appliedFilter;

        public PlayerIdAnalyticsTracker(EcsWorld world, PlayerManager playerManager, FacebookAuthService facebook, GoogleAuthService google)
        {
            _world = world;
            _playerManager = playerManager;

            _appliedFilter = world.GetFilter(typeof(EcsFilter<AnalyticsEventApplied<PlayerIdAnalyticsTracker>>)) as
                                                    EcsFilter<AnalyticsEventApplied<PlayerIdAnalyticsTracker>>;
            _facebook = facebook;
            _google = google;
        }

        public void Track()
        {
            if (_appliedFilter.GetEntitiesCount() > 0)
            {
                return;
            }

            if (_playerManager?.Session == null)
            {
                return;
            }

            string playerId = _playerManager.Session.AccountInfo.UserId;

            if (string.IsNullOrWhiteSpace(playerId))
            {
                return;
            }

            _world.NewEntity().Get<AnalyticsEventApplied<PlayerIdAnalyticsTracker>>();

            FireEvent(playerId);
        }

        private void FireEvent(string playedId)
        {
            EcsEntity analyticsEvent = _world.NewEntity();
            analyticsEvent.Get<AnalyticsCustomEvent>().Id = "player_id";
            analyticsEvent.Get<AnalyticsParameters>().Parameters = new Dictionary<string, string>
            {
                ["PlayerId"] = playedId,
                ["FacebookLogin"] = _facebook.IsLoggedIn.ToString(),
                ["GoogleLogin"] = _google.IsLoggedIn.ToString()
            };
        }
    }
}