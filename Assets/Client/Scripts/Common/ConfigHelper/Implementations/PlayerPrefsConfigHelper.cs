using Client.Common.Player.Controllers;
using Client.Utils.ServiceTool;
using Newtonsoft.Json;
using Client.Common.ConfigHelper.Abstractions;

namespace Client.Common.ConfigHelper.Implementations
{
    public class PlayerPrefsConfigHelper<TConfig> : ConfigHelper<TConfig>
    {
        private readonly bool _unique;
        private PlayerSession _accountService;
        private PlayerSession PlayerSession => _accountService ??= Service<PlayerManager>.Get().Session;
        
        private readonly JsonSerializerSettings _serializerSettings = new()
        {
            Formatting = Formatting.Indented
        };

        public PlayerPrefsConfigHelper(string path, bool unique = false) : base(path)
        {
            _unique = unique;
        }

        public override void Save(TConfig config, string postFix = null)
        {
            string configData = JsonConvert.SerializeObject(config, _serializerSettings);

            if (string.IsNullOrEmpty(configData))
            {
                Utils.GameLogger.GameLogger.LogWarning($"Config {typeof(TConfig)} is empty.");
            }

            UnityEngine.PlayerPrefs.SetString(GetPathWithUserId(postFix), configData);
            UnityEngine.PlayerPrefs.Save();
        }

        public override bool TryLoad(out TConfig config, string postFix = null)
        {
            string configData = UnityEngine.PlayerPrefs.GetString(GetPathWithUserId(postFix));

            if (string.IsNullOrEmpty(configData))
            {
                configData = TryLoadLegacy(postFix);
            }
            
            if (string.IsNullOrEmpty(configData))
            {
                config = default;
                return false;
            }
            
            try
            {
                config = JsonConvert.DeserializeObject<TConfig>(configData, _serializerSettings);
            }
            catch
            {
                configData = $"\"{configData}\"";
                config = JsonConvert.DeserializeObject<TConfig>(configData, _serializerSettings);
            }

            return true;
        }
        
        private string TryLoadLegacy(string postFix = null)
        {
            string legacyKey = string.Format(Path, postFix);
            string configData = UnityEngine.PlayerPrefs.GetString(legacyKey);

            if (!string.IsNullOrEmpty(configData))
            {
                UnityEngine.PlayerPrefs.SetString(GetPathWithUserId(postFix), configData);
                UnityEngine.PlayerPrefs.DeleteKey(legacyKey);
            }

            return configData;
        }

        public override void Delete(string postFix = null)
        {
            UnityEngine.PlayerPrefs.DeleteKey(GetPathWithUserId(postFix));
        }

        private string GetPathWithUserId(string postFix)
        {
            string formattedPath = string.Format(Path, postFix);

            if (_unique)
            {
                return formattedPath;
            }
            
            return $"{formattedPath}_{PlayerSession.AccountInfo.UserId}";
        }
    }
}