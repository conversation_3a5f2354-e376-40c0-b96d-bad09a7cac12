using System;
using UnityEngine;

namespace Client.Common.Configs.Components.Ai
{
    [Serializable]
    public struct ShieldData
    {
        public float ChanceUseShield;

        public ShieldData(float chanceUseShield)
        {
            ChanceUseShield = chanceUseShield;
        }

        public ShieldData(ShieldData other) : this(other.ChanceUseShield)
        {
        }

        public static ShieldData Clamp0(ShieldData data)
        {
            data.ChanceUseShield = Mathf.Max(data.ChanceUseShield, 0);

            return data;
        }

        public static ShieldData operator *(ShieldData a, ShieldData b)
        {
            a.ChanceUseShield *= b.ChanceUseShield;

            return a;
        }
        
        public static ShieldData operator +(ShieldData a, ShieldData b)
        {
            a.ChanceUseShield += b.ChanceUseShield;

            return a;
        }
        
        public static ShieldData operator -(ShieldData a, ShieldData b)
        {
            a.ChanceUseShield -= b.ChanceUseShield;

            return a;
        }
    }
}