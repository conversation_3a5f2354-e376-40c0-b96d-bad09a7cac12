using System.Collections.Generic;
using System.Linq;
using Client.Common.Configs.Components.Effects;

namespace Client.Common.Configs.Components
{
    public struct EffectKeysData
    {
        public List<string> Keys { get; private set; }
        public List<EffectIdData> Effects { get; private set; }

        public EffectKeysData Construct(List<string> keys)
        {
            Keys = keys;

            return this;
        }
        
        public EffectKeysData Construct(List<EffectIdData> data)
        {
            Keys = data.Select((x) => x.ToConfigKey()).ToList();
            Effects = data;

            return this;
        }
    }
}