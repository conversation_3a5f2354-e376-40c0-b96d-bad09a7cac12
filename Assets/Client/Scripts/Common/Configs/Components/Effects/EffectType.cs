namespace Client.Common.Configs.Components.Effects
{
    public enum EffectType
    {
        None,
        Bleeding,
        BleedingModificator,
        
        //qte
        InfiniteStamina,
        Freeze,
        SpeedupMovement,
        ChangeThrowCost,
        AiChangeThrowSpeed,
        AiChangeThrowAccuracy,
        AiChangeShieldUsage,

        //bottles
        AutoHeal,
        ChangeStaminaRecoverySpeed,
        ShieldDamageReductionModifier,
        StaminaUsageSkip,
        
        //power stones
        ShieldPiercing,
        ChangeDamageAmount,
        
        //harpoon effects
        StaminaDepletion,
        ShieldDepletion,
        HealthDepletion,
        
        //region
        HookParry,
        RegionCooldownModifier,
        ChangeTopDownMovementSpeed,
        BlockTopDownMotion,
        BlockGear
    }
}