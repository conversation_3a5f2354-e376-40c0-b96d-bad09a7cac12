using System;
using System.Collections.Generic;
using System.Threading;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Effects;
using Client.Common.Configs.Components.Items;
using Client.Common.Configs.Components.Items.Hook;
using Client.Common.Configs.Components.Items.TypeMarkers;
using Client.Common.CSV;
using Client.Utils.GameLogger;
using Client.Utils.ResourceLoading;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;

namespace Client.Common.Configs.Loaders
{
    public class HookDataLoader : IConfigLoader
    {
        private readonly Dictionary<HookId, string> _hookHitFXPaths = new()
        {
            {HookId.Default, "VFX/HookHit/BloodHooked"},
            {HookId.ShieldBreaker, "VFX/HookHit/ShieldBreakerHit"},
            {HookId.Exhaustion, "VFX/HookHit/ExhaustionHit"}
        };

        private const string _HOOK_BLOCK_FX_PATH_ = "VFX/HookHit/HookBlockSparks";
        private const string _HOOK_BLOCK_BUFF_FX_PATH_ = "VFX/HookHit/HookBlockBuffedSparks";
        private const string _UNIT_HOOK_PATH_ = "Locations/Fx/Region/UnitHook";

        private static readonly Dictionary<HookId, HookEffectsData> HookEffectsDataMap = new() //todo: replace with hooks sheet
        {
            {
                HookId.Default, new HookEffectsData().Construct(
                    new[]
                    {
                        new EffectIdData().Construct(EffectType.HealthDepletion, 1)
                    },
                    new List<EffectIdData>())
            },
            
            {
                HookId.ShieldBreaker, new HookEffectsData().Construct(
                    new[]
                    {
                        new EffectIdData().Construct(EffectType.ShieldDepletion, 1)
                    },
                    new List<EffectIdData>())
            }, 
            
            {
                HookId.Exhaustion, new HookEffectsData().Construct(
                    new[]
                    {
                        new EffectIdData().Construct(EffectType.StaminaDepletion, 1)
                    },
                    new []
                    {
                        new EffectIdData().Construct(EffectType.AiChangeThrowSpeed, 69024)
                    })
            },
        };


        private readonly GoogleDocsData _googleDocsData;

        public HookDataLoader(GoogleDocsData googleDocsData)
        {
            _googleDocsData = googleDocsData;
        }

        public UniTask Load(EcsEntity entity, string configKey, CancellationToken cancellationToken = default)
        {
            if (!entity.Has<HookItemMarker>())
            {
                return UniTask.CompletedTask;
            }

            entity.Get<SpeedData>() = LoadSpeed(configKey);
            entity.Get<RangeData>() = LoadRange(configKey);
            
            if (!Enum.TryParse(configKey, out HookId hookId))
            {
                GameLogger.LogError($"[HookDataLoader] Can't parse HookId from config key {configKey}");
                return UniTask.CompletedTask;
            }
            TryLoadEffects(entity, hookId);
            LoadPaths(entity, hookId);

            return UniTask.CompletedTask;
        }

        private RangeData LoadRange(string configKey)
        {
            float range = _googleDocsData.GetNumber("hook.distance");
            return new RangeData().Construct(range);
        }

        private SpeedData LoadSpeed(string configKey)
        {
            float speed = 8f;
            return new SpeedData().Construct(speed);
        }

        private void TryLoadEffects(EcsEntity configEntity, HookId hookId)
        {
            if (HookEffectsDataMap.TryGetValue(hookId, out HookEffectsData effectsData))
            {
                configEntity.Get<HookEffectsData>() = effectsData;
            }
        }

        private void LoadPaths(EcsEntity entity, HookId hookId)
        {
            ref ResourceConfigsData resourceConfigsData = ref entity.Get<ResourceConfigsData>();
            resourceConfigsData.Add(HookPath.BuffedBlockVfx, LoadFromType.Ressources, _HOOK_BLOCK_BUFF_FX_PATH_);
            resourceConfigsData.Add(HookPath.HitVfx, LoadFromType.Ressources, _hookHitFXPaths[hookId]);
            resourceConfigsData.Add(HookPath.BlockVfx, LoadFromType.Ressources, _HOOK_BLOCK_FX_PATH_);
            resourceConfigsData.Add(HookPath.HookView, LoadFromType.Ressources, _UNIT_HOOK_PATH_);
        }
    }
}