using System.Collections.Generic;
using System.Threading;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Effects;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;

namespace Client.Common.Configs.Loaders
{
    public class ItemEffectKeysLoader : IConfigLoader
    {
        private readonly Dictionary<string, List<EffectIdData>> _itemEffectsMap;

        public ItemEffectKeysLoader(Dictionary<string, List<EffectIdData>> itemEffectsMap)
        {
            _itemEffectsMap = itemEffectsMap;
        }

        public UniTask Load(EcsEntity entity, string configKey, CancellationToken cancellationToken = default)
        {
            if (_itemEffectsMap.TryGetValue(configKey, out List<EffectIdData> effectIds))
            {
                entity.Get<EffectKeysData>().Construct(effectIds);
            }
            
            return UniTask.CompletedTask;
        }
    }
}