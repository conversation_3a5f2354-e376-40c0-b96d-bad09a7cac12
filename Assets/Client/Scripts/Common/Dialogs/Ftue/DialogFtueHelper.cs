using System.Collections;
using Client.Common.Analytics.Helpers;
using Client.Common.Dialogs.Runtime.Components;
using Client.Common.Dialogs.Runtime.Systems;
using Client.Common.EcsFreeze;
using Client.Utils.ECS.EcsTaskTools;
using Client.Utils.ECS.LocalWorld;
using Leopotam.Ecs;

namespace Client.Common.Dialogs
{
    public class DialogFtueHelper
    {
        private readonly LocalEcsWorld _world;
        private readonly IAnalyticsHelper _analyticsHelper;

        private readonly EcsFilter<DialogWidget> _dialogs = default;
        private readonly EcsFilter<DialogWidget, DialogClose> _dialogsClosing = default;

        public DialogFtueHelper(LocalEcsWorld world, IAnalyticsHelper analyticsHelper)
        {
            _world = world;
            _analyticsHelper = analyticsHelper;

            _dialogs = world.GetFilter<EcsFilter<DialogWidget>>();
            _dialogsClosing = world.GetFilter<EcsFilter<DialogWidget, DialogClose>>();
        }

        public IEnumerator OpenDialog(
            int dialogId,
            DialogButtonType buttonType = DialogButtonType.Regular,
            bool overlay = false,
            bool transitionOnClose = true)
        {
            OpenDialogRequest(dialogId, buttonType, overlay, transitionOnClose);

            yield break;
        }

        private EcsEntity OpenDialogRequest(
            int dialogId,
            DialogButtonType buttonType,
            bool overlay,
            bool transitionOnClose)
        {
            EcsEntity openRequest = _world.NewEntity();
            ref DialogOpen dialogOpen = ref openRequest.Get<DialogOpen>();

            dialogOpen.Id = dialogId;
            dialogOpen.Button = buttonType;
            dialogOpen.Overlay = overlay;
            dialogOpen.Overlay = overlay;
            dialogOpen.TransitionOnClose = transitionOnClose;

            return openRequest;
        }

        public IEnumerator OpenDialogWithAwait(
            int dialogId,
            DialogButtonType buttonType = DialogButtonType.Regular,
            bool overlay = false,
            bool transitionOnClose = true)
        {
            EcsEntity openRequest = OpenDialogRequest(dialogId, buttonType, overlay, transitionOnClose);
            EcsTask task = openRequest.Get<EcsTaskConfig>().Construct();

            while (task.Status != EcsTask.States.Done)
            {
                yield return null;
            }
        }

        /// <summary>
        /// Opens a dialog and waits for it to complete together with its ending fade animation.
        /// </summary>
        /// <param name="dialogId">Dialog ID (please use DialogIdents)</param>
        public IEnumerator OpenAndWaitDialogComplete(int dialogId)
        {
            IEnumerator openTask = OpenDialogWithAwait(dialogId, transitionOnClose: true);

            while (openTask.MoveNext())
            {
                yield return null;
            }

            IEnumerator waitTask = WaitForNoDialog();

            while (waitTask.MoveNext())
            {
                yield return null;
            }
        }

        /// <summary>
        /// Opens a dialog and waits until the start of fade animation at the end of the dialog.
        /// </summary>
        /// <param name="dialogId">Dialog ID (please use DialogIdents)</param>
        /// <param name="transitionOnClose">Set this to 'false' to skip the ending fade altogether</param>
        public IEnumerator OpenAndWaitDialogClose(int dialogId, bool transitionOnClose = true)
        {
            IEnumerator openTask = OpenDialogWithAwait(dialogId, transitionOnClose: transitionOnClose);

            while (openTask.MoveNext())
            {
                yield return null;
            }

            IEnumerator waitTask = WaitForDialogClose();

            while (waitTask.MoveNext())
            {
                yield return null;
            }
        }

        private IEnumerator WaitForDialogClose()
        {
            while (_dialogsClosing.IsEmpty())
            {
                yield return null;
            }
        }

        private IEnumerator WaitForNoDialog()
        {
            while (!_dialogs.IsEmpty())
            {
                yield return null;
            }
        }

        public IEnumerator OpenDialogAndWaitWithFreeze(int dialogId, bool overlay = true)
        {
            EcsEntity freezeEntity = _world.NewEntity();
            freezeEntity.Get<Freeze>();
            IEnumerator openTask = OpenDialogWithAwait(dialogId, overlay: overlay);

            while (openTask.MoveNext())
            {
                yield return null;
            }

            IEnumerator waitTask = WaitForNoDialog();

            while (waitTask.MoveNext())
            {
                yield return null;
            }

            freezeEntity.Del<Freeze>();
        }

        public IEnumerator TrackDialogs(bool customTracker, params int[] dialogIds)
        {
            DialogAnalyticsTracker dialogAnalyticsTracker = new DialogAnalyticsTracker(_world, _analyticsHelper);

            dialogAnalyticsTracker.Init(customTracker, dialogIds);

            while (dialogAnalyticsTracker.IsCompleted == false)
            {
                dialogAnalyticsTracker.Track();

                yield return null;
            }
        }
    }
}