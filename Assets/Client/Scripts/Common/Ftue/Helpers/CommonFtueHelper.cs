using System;
using System.Collections;
using Client.Common.EcsFreeze;
using Client.Common.Ftue.Infrastructure;
using Client.Common.Ftue.Infrastructure.Data;
using Client.Common.UI.InputLockService;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.Visible;
using Common;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using UnityEngine;

namespace Client.Common.Ftue.Helpers
{
    public class CommonFtueHelper
    {
        private readonly LocalEcsWorld _world;
        private readonly FtueProgress _ftueProgress;

        public CommonFtueHelper(LocalEcsWorld world, FtueProgress ftueProgress)
        {
            _world = world;
            _ftueProgress = ftueProgress;
        }

        public IEnumerator WaitTime(float delay)
        {
            float time = Time.unscaledTime + delay;
            while (time > Time.unscaledTime)
            {
                yield return null;
            }
        }
        
        public IEnumerator CompleteStageRoutine(FtueProgressKeys progressKey)
        {
            CompleteStage(progressKey);
            yield break;
        }

        public IEnumerator CompleteStageAndForget(AccountStep step)
        {
            _ftueProgress.Complete(step).Forget();
            yield break;
        }

        public IEnumerator CompleteStageRoutine(AccountStep step)
        {
            UniTask task = _ftueProgress.Complete(step);
            while (task.Status != UniTaskStatus.Succeeded)
            {
                yield return null;
            }
        }

        public void CompleteStage(FtueProgressKeys progressKey)
        {
            _ftueProgress.Complete(progressKey);
        }

        public void FreezeBattle(out EcsEntity freeze)
        {
            freeze = _world.NewEntity();
            freeze.Get<Freeze>();
            freeze.Get<FtueFreeze>();
        }
        
        public void UnfreezeBattle(EcsEntity freeze)
        {
            if (!freeze.IsNull())
            {
                freeze.Del<Freeze>();
                freeze.Del<FtueFreeze>();
            }
        }
        
        public IEnumerator SetVisibility(VisibilityController controller, bool on)
        {
            if (on)
            {
                controller.Show();
            }
            else
            {
                controller.Hide();
            }
            yield break;
        }
        
        public IEnumerator LockScreen(FullScreenLocker screenLocker)
        {
            screenLocker.Lock();
            yield break;
        }
        
        public IEnumerator UnLockScreen(FullScreenLocker screenLocker)
        {
            screenLocker.Unlock();
            yield break;
        }

        public IEnumerator ActionRoutine(Action action)
        {
            action?.Invoke();
            yield break;
        }

        public IEnumerator BlockOtherTutorials()
        {
            while (true)
            {
                yield return null;
            }
        }
    }
}