namespace Client.Common.Ftue.Infrastructure.Data
{
    public enum FtueProgressKeys
    {
        Region01,
        Region01MoveToEnemy,
        Battle01,
        Region02,
        Battle02,
        Region03,
        Battle03,
        Region04,
        <PERSON>sRank<PERSON>nlock,
        
        //Major step - Daily bonus unlocked = 10
        BloodProgress<PERSON>irstEnter,
        BlacksmithUnlock,
        ProfileFirstEnter,
        TargetFromMarketToRegion,
        MapFirstOpen,
        FirstEnterBattleRegion,
        
        ChestsTutorial,
        BankVirtualStoreFirstOpen,
        
        //Major step - ShurikenCraftTutorialFinished = 19
        //Major step - RPTutorialFinished = 20

        AlchemistCraftHealBottle,
        AlchemistCompleteCraftHealBottle,
        AlchemistDrinkHealBottle,
        
        QteTutorialStart,
        QteBattleComplete,
        QteTutorialComplete,
        
        JunkmanAmuletsUnlock,

        ParryTutorialStart,
        ParryBattleComplete,
        ParryTutorialComplete,
        
        JunkmanAmuletsCraft,

        DailyQuestClaimed,
        DailyQuestsReroll,
        
        BlacksmithSkinTabUnlocked,
        BlackSmithSkinFirstCraft,
        
        JunkmanOutfitTabUnlocked,

        PowerStonesTutorialStart,
        PowerStonesBattleComplete,
        PowerStonesKarasFlyAway,
        PowerStonesTutorialComplete,
        
        HooksTutorialStart,
        HooksTutorialComplete,
        
        BombsTutorialStart,
        BombsTutorialComplete,
    }
}