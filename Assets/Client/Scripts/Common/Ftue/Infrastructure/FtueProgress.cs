using System.Collections.Generic;
using System.Linq;
using Client.Common.Configs;
using Client.Common.Ftue.Helpers;
using Client.Common.Ftue.Infrastructure.Data;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Utils.GameLogger;
using Client.Utils.Loaders;
using Client.Utils.ResultTool.Results;
using Common;
using Cysharp.Threading.Tasks;

namespace Client.Common.Ftue.Infrastructure
{
    public sealed class FtueProgress
    {
        private readonly HashSet<string> _stages = new();
        private HashSet<AccountStep> _steps = new();

        private readonly LocalFtueStagesStorage _stagesStorage;
        private readonly PlayerSession _playerSession;
        private readonly MetaNet _metaNet;

        private readonly FtueProgressConverter _converter;

        public FtueConditions Conditions { get; }

        public FtueProgress(
            PlayerManager playerManager,
            MetaNet metaNet,
            ConfigService configService)
        {
            _converter = new FtueProgressConverter(this);
            _playerSession = playerManager.Session;
            _metaNet = metaNet;

            Conditions = new FtueConditions(configService, this, playerManager);

            _stagesStorage = new LocalFtueStagesStorage();
        }

        public async UniTask<LoadResult> Load()
        {
            _stages.Clear();
            Result<HashSet<string>> stagesLoading = await _stagesStorage.Load();
            if (stagesLoading.IsSuccess)
            {
                LoadInternal(stagesLoading.Value.ToList());
            }

            await TryGenerateFtueSteps();

            return new LoadResult(true);
        }

        public async UniTask OverrideProgress(List<string> ftueKeys, List<AccountStep> accountSteps)
        {
            _stages.Clear();
            LoadInternal(ftueKeys);
            await _stagesStorage.Save(_stages);
            _steps = _converter.GenerateAccountSteps();

            if (IsCompleted(AccountStep.FirstEnterBattleRegion) && _playerSession.Inventory.BloodBalance.Amount > 0)
            {
                _metaNet.DrainBlood().Forget();
            }

            foreach (AccountStep step in accountSteps)
            {
                if (!IsCompleted(step))
                {
                    _steps.Add(step);
                }
            }

            await _metaNet.SetAccountSteps(_steps);
            GameLogger.Log($"[FTUE STEPS GENERATE]: {string.Join(",", _steps)}");
        }

        // This function is internal to ftue. For external usages please check IsCompleted(AccountStep).
        internal bool IsCompleted(FtueProgressKeys token) => _stages.Contains(token.ToString());

        internal bool IsCompleted(string progressKey) => _stages.Contains(progressKey);

        public bool IsCompleted(AccountStep step) => _steps.Contains(step);

        public void Complete(FtueProgressKeys tokenKey)
        {
            string token = tokenKey.ToString();

            if (IsCompleted(tokenKey))
            {
                return;
            }

            _stages.Add(token);
            _stagesStorage.Save(_stages).Forget();

            if (FtueProgressConverter.TryGetStep(tokenKey, out AccountStep step))
            {
                Complete(step).Forget();
            }
        }

        public async UniTask Complete(AccountStep step)
        {
            if (IsCompleted(step))
            {
                return;
            }

            _steps.Add(step);
            await _metaNet.SetAccountSteps(_steps);
            GameLogger.Log($"[FTUE STEP COMPLETE]: {step}");
        }

        private async UniTask TryGenerateFtueSteps()
        {
            _steps = _playerSession.Progress.FtueSteps;
            GameLogger.Log($"[MAJOR FTUE STEPS LOADED]: {string.Join(",", _steps)}");

            if (_steps.Count > 0 && !_converter.IsProgressLegacy)
            {
                return;
            }

            await RegenerateAccountSteps();
        }

        private async UniTask RegenerateAccountSteps()
        {
            _steps = _converter.GenerateAccountSteps();
            await _metaNet.SetAccountSteps(_steps);
            GameLogger.Log($"[FTUE STEPS GENERATE]: {string.Join(",", _steps)}");
        }


        private void LoadInternal(List<string> data)
        {
            foreach (string token in data)
            {
                _stages.Add(token);
            }

            GameLogger.Log($"[FTUE] progress-load: \"{string.Join(",", data)}\"");
        }

        public void ClearStages()
        {
            _stages.Clear();
            _stagesStorage.Save(_stages).Forget();
            GameLogger.LogError("Local ftue stages cleared!");
        }
    }
}