using System.Collections.Generic;
using Client.Common.Ftue.Infrastructure.Data;
using Common;
using Enum = System.Enum;

namespace Client.Common.Ftue.Infrastructure
{
    public class FtueProgressConverter
    {
        private static readonly Dictionary<FtueProgressKeys, AccountStep> StepDictionary = new()
        {
            {FtueProgressKeys.KarasRankUnlock, AccountStep.ExitFromTutorialRegion},
            {FtueProgressKeys.FirstEnterBattleRegion, AccountStep.FirstEnterBattleRegion},
            {FtueProgressKeys.AlchemistCompleteCraftHealBottle, AccountStep.AlchemistCraftTutorialFinished},
            {FtueProgressKeys.AlchemistDrinkHealBottle, AccountStep.AlchemistTraumaTutorialFinished},
            {FtueProgressKeys.JunkmanAmuletsUnlock, AccountStep.AmuletTabUnlocked},
            {FtueProgressKeys.JunkmanAmuletsCraft, AccountStep.AmuletCraftTutorialFinished},
            {FtueProgressKeys.ChestsTutorial, AccountStep.ChestsTutorialFinished},
            {FtueProgressKeys.DailyQuestClaimed, AccountStep.DailyQuestsUnlocked},
            {FtueProgressKeys.DailyQuestsReroll, AccountStep.DailyQuestsSwapTutorialFinished},
            {FtueProgressKeys.BlacksmithSkinTabUnlocked, AccountStep.ShurikenSkinTabUnlocked},
            {FtueProgressKeys.BlackSmithSkinFirstCraft, AccountStep.ShurikenSkinTutorialFinished},
            {FtueProgressKeys.JunkmanOutfitTabUnlocked, AccountStep.OutfitTabUnlocked},
            {FtueProgressKeys.QteTutorialComplete, AccountStep.QteTutorialFinished},
            {FtueProgressKeys.ParryTutorialComplete, AccountStep.ParryTutorialFinished},
            {FtueProgressKeys.PowerStonesTutorialComplete, AccountStep.PowerStonesTutorialFinished},
            {FtueProgressKeys.HooksTutorialComplete, AccountStep.HooksTutorialFinished},
            {FtueProgressKeys.BombsTutorialComplete, AccountStep.BombsTutorialFinished},
        };

        public static bool TryGetStep(FtueProgressKeys key, out AccountStep step)
        {
            return StepDictionary.TryGetValue(key, out step);
        }

        public static List<string> GetAllSteps()
        {
            var allSteps = Enum.GetValues(typeof(FtueProgressKeys)) as FtueProgressKeys[];
            List<string> steps = new List<string>(allSteps.Length);

            for (int index = 0; index < allSteps.Length; index++)
            {
                FtueProgressKeys key = allSteps[index];
                if (TryGetStep(key, out AccountStep step))
                {
                    bool sameName = key.ToString() == step.ToString();
                    if (!sameName)
                    {
                        steps.Add(key.ToString());
                    }

                    steps.Add(FormatAsMajor(step));
                }
                else
                {
                    steps.Add(key.ToString());
                }
            }

            steps.Insert(10, FormatAsMajor(AccountStep.DailyBonusUnlocked));
            steps.Insert(19, FormatAsMajor(AccountStep.ShurikenCraftTutorialFinished));
            steps.Insert(20, FormatAsMajor(AccountStep.RpTutorialFinished));

            return steps;

            string FormatAsMajor(AccountStep step)
            {
                return ($"<color=#FF0000>{step.ToString()}</color>");
            }
        }

        private readonly FtueProgress _ftue;

        public FtueProgressConverter(FtueProgress ftue)
        {
            _ftue = ftue;
        }

        public HashSet<AccountStep> GenerateAccountSteps()
        {
            RewriteLegacy(LegacyFtueProgressKeys.Profile03, FtueProgressKeys.BloodProgressFirstEnter);
            RewriteLegacy(LegacyFtueProgressKeys.SmithCraft01, FtueProgressKeys.BlacksmithUnlock);
            RewriteLegacy(LegacyFtueProgressKeys.Market04, FtueProgressKeys.TargetFromMarketToRegion);
            RewriteLegacy(LegacyFtueProgressKeys.JunkmanAmulets01, FtueProgressKeys.JunkmanAmuletsUnlock);

            var steps = new HashSet<AccountStep>();

            steps.Add(AccountStep.Initial);
            if (IsRegionTutorialCompleted(_ftue))
            {
                steps.Add(AccountStep.ExitFromTutorialRegion);
            }

            foreach ((FtueProgressKeys key, AccountStep step) in StepDictionary)
            {
                if (_ftue.IsCompleted(key) && !steps.Contains(step))
                {
                    steps.Add(step);
                }
            }

            Add(FtueProgressKeys.MapFirstOpen, AccountStep.FirstEnterBattleRegion);
            Add(FtueProgressKeys.AlchemistCraftHealBottle, AccountStep.AlchemistUnlocked);
            Add(FtueProgressKeys.PowerStonesTutorialComplete, AccountStep.RpTutorialFinished); //crutch fix

            AddLegacy(LegacyFtueProgressKeys.ShurikenCraftMovedBackAfterEquip, AccountStep.ShurikenCraftTutorialFinished);
            AddLegacy(LegacyFtueProgressKeys.JunkmanAmulets01, AccountStep.AmuletTabUnlocked);
            AddLegacy(LegacyFtueProgressKeys.JunkmanAmulets02, AccountStep.AmuletCraftTutorialFinished);
            AddLegacy(LegacyFtueProgressKeys.SmithSkin01, AccountStep.ShurikenSkinTabUnlocked);
            AddLegacy(LegacyFtueProgressKeys.JunkmanOutfit01, AccountStep.OutfitTabUnlocked);
            AddLegacy(LegacyFtueProgressKeys.ShurikenCraftedAndEquipped, AccountStep.ShurikenCraftTutorialFinished);

            return steps;

            void Add(FtueProgressKeys key, AccountStep step)
            {
                if (_ftue.IsCompleted(key) && !steps.Contains(step))
                {
                    steps.Add(step);
                }
            }

            void AddLegacy(LegacyFtueProgressKeys key, AccountStep step)
            {
                if (_ftue.IsCompleted(key.ToString()) && !steps.Contains(step))
                {
                    steps.Add(step);
                }
            }

            void RewriteLegacy(LegacyFtueProgressKeys key, FtueProgressKeys newKey)
            {
                if (_ftue.IsCompleted(key.ToString()))
                {
                    _ftue.Complete(newKey);
                }
            }
        }

        public static bool IsRegionTutorialCompleted(FtueProgress ftue) => ftue.IsCompleted(FtueProgressKeys.KarasRankUnlock)
                                                                           || ftue.IsCompleted(LegacyFtueProgressKeys.MarketKarasRankUnlock.ToString());

        public bool IsProgressLegacy => _ftue.IsCompleted(LegacyFtueProgressKeys.ShurikenCraftMovedBackAfterEquip.ToString()) &&
                                        !_ftue.IsCompleted(AccountStep.ShurikenCraftTutorialFinished);

        private enum LegacyFtueProgressKeys
        {
            Profile03,
            SmithCraft01,
            Market04,

            ShurikenCraftMovedBackAfterEquip,
            ShurikenCraftedAndEquipped,

            MarketKarasRankUnlock,
            JunkmanAmulets01,
            JunkmanAmulets02,
            SmithSkin01,
            JunkmanOutfit01,
            
            DailyMenuUnlock,
            MarketAltarTournament
        }
    }
}