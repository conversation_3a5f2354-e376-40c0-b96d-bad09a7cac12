using Client.Common.NpcTab.Pieces.Views;
using Client.Common.UI.Buttons;
using UnityEngine;

namespace Client.Common.UI.Rewards.Core
{
    public class FtueMaterialsPopupView: RewardPopupViewBase
    {
        [SerializeField] private PieceView _componentsView;
        [SerializeField] private ActionButton _button;

        public override void OnOpen()
        {
            base.OnOpen();
            _button.Clicked += OnButtonClicked;
        }

        public override void OnPreClose()
        {
            base.OnPreClose();
            _button.Clicked -= OnButtonClicked;
        }

        private void OnButtonClicked()
        {
            _button.SetInteractable(false);
            SoundHelper?.PlayClickSound();
            SendInteractedEvent();
        }
        
        public void SetComponents(GameObject iconPrefab, int quantity, int quantityMax)
        {
            _componentsView.SetCount(quantity, quantityMax);
            _componentsView.SetIcon(iconPrefab);
        }
    }
}