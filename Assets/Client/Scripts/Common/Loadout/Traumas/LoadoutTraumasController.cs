using System;
using System.Collections.Generic;
using System.Threading;
using Client.Common.Audio;
using Client.Common.CSV;
using Client.Common.Items;
using Client.Common.Loadout.Items;
using Client.Common.Loadout.Services;
using Client.Common.NpcTab.Controllers;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.Common.Traumas;
using Client.Common.UI.Buttons.Pay;
using Client.Common.UI.InputLockService;
using Client.Utils.GameLogger;
using Client.Utils.ResourceLoading;
using Client.Utils.ResultTool.Results;
using Client.Utils.ViewService;
using Cysharp.Threading.Tasks;
using UnityEngine;
using CurrencyType = Common.CurrencyType;
using TimerType = Common.TimerType;

namespace Client.Common.Loadout.Traumas
{
    public class LoadoutTraumasController
    {
        private const string _PRICE_FORMAT_ = "loadout.healprice.{0}";

        private readonly PopupViewService _viewService;
        private readonly PlayerManager _playerManager;
        private readonly ResourceLoadingService _resourceLoadingService;
        private readonly CancellationToken _cancellationToken;
        private readonly FullScreenLocker _screenLocker;
        private readonly GoogleDocsData _googleDocsData;
        private readonly HealTraumaHelper _healTraumaHelper;
        private readonly LoadoutUiService _loadoutUiService;
        private readonly BankTransferController _bankTransferController;
        private readonly UISoundHelper _soundHelper;

        private LoadoutTraumasPresenter _presenter;
        private bool _inProcess;
        private bool _isViewOpen;
        public event Action Closed;

        public LoadoutTraumasController(
            PopupViewService viewService,
            PlayerManager playerManager,
            HealTraumaHelper healTraumaHelper,
            ResourceLoadingService resourceLoadingService,
            CancellationToken cancellationToken,
            FullScreenLocker screenLocker,
            GoogleDocsData googleDocsData,
            LoadoutUiService loadoutUiService,
            BankTransferController bankTransferController,
            UISoundHelper soundHelper)
        {
            _viewService = viewService;
            _playerManager = playerManager;
            _resourceLoadingService = resourceLoadingService;
            _healTraumaHelper = healTraumaHelper;
            _cancellationToken = cancellationToken;
            _screenLocker = screenLocker;
            _googleDocsData = googleDocsData;
            _loadoutUiService = loadoutUiService;
            _bankTransferController = bankTransferController;
            _soundHelper = soundHelper;
        }

        public bool CanHealAnyTrauma()
        {
            List<PlayerTimer> traumas = GetPlayerTraumas();

            if (traumas.Count == 0)
            {
                return false;
            }

            PlayerInventory inventory = _playerManager.Session.Inventory;

            return inventory.Hard.IsEnough((int) GetHardPrice(traumas.Count))
                   || inventory.GetItemCount(PlayerInventory.Items.BOTTLE_FOR_HEAL) > 0;
        }

        public void SetTopButton(bool back)
        {
            _presenter.View.SetTopButton(back);
        }

        public async UniTask ShowScreen()
        {
            _screenLocker.Lock(_cancellationToken);
            await PrepareScreen();
            await _presenter.Show(token: _cancellationToken);
            _isViewOpen = true;
            _screenLocker.Unlock(_cancellationToken);
        }

        public async UniTask PrepareScreen()
        {
            List<PlayerTimer> traumas = GetPlayerTraumas();

            _presenter?.Dispose();

            _presenter = await _viewService.Prepare<LoadoutTraumasPresenter, LoadoutTraumasView, LoadoutTraumasModel>(
                new LoadoutTraumasModel()
                {
                    Traumas = traumas,
                    HardHealPrice = GetHardPrice(traumas.Count),
                    BottlePrice = 1,
                    BottleAsset = await GetBottle()
                }, cancellationToken: _cancellationToken);

            _presenter.HealByBottleRequested = HealByBottle;
            _presenter.HealByHardRequested = HealByHard;
            _presenter.ExitClicked = HideScreen;
        }

        public async UniTask OpenAndWaitClose()
        {
            if (_presenter is null)
            {
                GameLogger.LogError("[Traumas Controller] Trying to open a view that is not ready");
                return;
            }
            _screenLocker.Lock(_cancellationToken);
            await _presenter.Show(token: _cancellationToken);
            _isViewOpen = true;
            _screenLocker.Unlock(_cancellationToken);
            await UniTask.WaitUntil(() => !_isViewOpen, cancellationToken: _cancellationToken);
        }

        public void UpdateView()
        {
            if (!_isViewOpen || _inProcess)
            {
                return;
            }

            List<PlayerTimer> traumas = GetPlayerTraumas();

            if (traumas.Count == 0)
            {
                HideScreen();
            }
            else
            {
                _presenter.UpdateTimers(traumas);
                _presenter.UpdateHardPrice(GetHardPrice(traumas.Count));
            }
        }

        private async void HideScreen()
        {
            _isViewOpen = false;
            _soundHelper.PlayBackClickSound();
            await _presenter.Hide(token: _cancellationToken);
            _presenter.Dispose();
            _presenter = null;
            Closed?.Invoke();
        }

        private UniTask<GameObject> GetBottle()
        {
            string viewPath = string.Format(ItemIdents.Paths.ITEM_PATH, PlayerInventory.Items.BOTTLE_FOR_HEAL);

            return _resourceLoadingService.Get(new ResourceConfig().Construct(LoadFromType.Ressources, viewPath)).LoadUniAsync<GameObject>(_cancellationToken);
        }

        private async void HealByHard()
        {
            _soundHelper.PlayClickSound();
            List<PlayerTimer> traumas = GetPlayerTraumas();

            if (traumas.Count == 0)
            {
                GameLogger.LogError("No traumas found to heal");

                return;
            }

            uint healPrice = GetHardPrice(traumas.Count);

            if (_playerManager.Session.Inventory.Hard.Amount < healPrice)
            {
                if (_bankTransferController == null ||
                    !await _bankTransferController.TryShowBankTransferConfirmation(CurrencyType.Hard, healPrice, cancellationToken: _cancellationToken))
                {
                    _presenter.View.HealByHardButton.SetState(ThePayButtonState.Locked);
                    _presenter.View.HealByHardButton.SetAttractionActive(true);
                }

                return;
            }

            _inProcess = true;
            _screenLocker.Lock(_cancellationToken);
            await HealAllTraumas(traumas, healPrice);
            _screenLocker.Unlock(_cancellationToken);
            _inProcess = false;

            UpdateView();
        }

        private async UniTask HealAllTraumas(List<PlayerTimer> traumas, uint healPrice)
        {
            Result healing = await _healTraumaHelper.HealForHard(traumas.ToArray(), (int) healPrice, _cancellationToken);

            if (healing.IsFailure)
            {
                return;
            }

            _loadoutUiService.UpdateItem(LoadoutItemId.Trauma);
            List<UniTask> animations = new List<UniTask>();

            foreach (PlayerTimer playerTimer in traumas)
            {
                animations.Add(_presenter.HealTrauma(playerTimer.ItemId));
            }

            await UniTask.WhenAll(animations);
        }

        private async void HealByBottle()
        {
            _soundHelper.PlayClickSound();

            if (_playerManager.Session.Inventory.GetItemCount(PlayerInventory.Items.BOTTLE_FOR_HEAL) == 0)
            {
                _presenter.View.HealByBottleButton.SetState(ThePayButtonState.Locked);
                _presenter.View.HealByBottleButton.SetAttractionActive(true);

                return;
            }

            int minTimerId = GetTraumaWithMinimalDuration();

            if (minTimerId == 0)
            {
                GameLogger.LogError("No traumas found to heal");

                return;
            }

            _inProcess = true;
            _screenLocker.Lock(_cancellationToken);
            UniTask request = _healTraumaHelper.HealForBottle(minTimerId, _cancellationToken);
            UniTask animation = _presenter.HealTrauma(minTimerId);
            await UniTask.WhenAll(request, animation);
            _loadoutUiService.UpdateItem(LoadoutItemId.Trauma);
            _healTraumaHelper.UseItem(PlayerInventory.Items.BOTTLE_FOR_HEAL);
            _inProcess = false;
            _screenLocker.Unlock(_cancellationToken);
            UpdateView();
        }

        private int GetTraumaWithMinimalDuration()
        {
            List<PlayerTimer> playerTimers = GetPlayerTraumas();
            float minDuration = float.MaxValue;
            int minTimerId = 0;

            foreach (PlayerTimer playerTimer in playerTimers)
            {
                long timerDuration = playerTimer.GetRemainingUtcTimeInSeconds();

                if (timerDuration < minDuration)
                {
                    minDuration = timerDuration;
                    minTimerId = playerTimer.ItemId;
                }
            }

            if (minTimerId == 0)
            {
                GameLogger.LogError("Timers: no timer exists for any trauma");
            }

            return minTimerId;
        }

        private List<PlayerTimer> GetPlayerTraumas()
        {
            List<PlayerTimer> playerTimers = _playerManager.Session.Timers.GetTimers(PlayerTimers.Query.Get().WithTimerType(TimerType.Trauma));

            return playerTimers;
        }

        private uint GetHardPrice(int traumaCount)
        {
            return (uint) _googleDocsData.GetIntNumber(string.Format(_PRICE_FORMAT_, traumaCount));
        }
    }
}