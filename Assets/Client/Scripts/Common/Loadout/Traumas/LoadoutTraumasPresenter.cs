using System;
using System.Collections.Generic;
using Client.Common.Player.Timers;
using Client.Common.TimeGiver.Abstractions;
using Client.Common.Traumas.Views;
using Client.Utils.ViewService;
using Cysharp.Threading.Tasks;
using Leopotam.Localization;
using UnityEngine;
using CurrencyType = Common.CurrencyType;
using Object = UnityEngine.Object;

namespace Client.Common.Loadout.Traumas
{
    public class LoadoutTraumasModel : PresenterModel
    {
        public List<PlayerTimer> Traumas;
        public uint BottlePrice;
        public uint HardHealPrice;
        public GameObject BottleAsset;
    }
    
    public class LoadoutTraumasPresenter: Presenter<LoadoutTraumasView, LoadoutTraumasModel>, IDisposable
    {
        private const string _TITLE_KEY_ = "loadout.traumas.title";
        private const string _DESCRIPTION_KEY_ = "loadout.traumas.description";
        private const string _BOTTLE_BUTTON_TITLE_ = "loadout.traumas.bottle.button.title";
        private const string _BOTTLE_BUTTON_SUBTITLE_ = "loadout.traumas.bottle.button.subtitle";
        private const string _HARD_BUTTON_TITLE_ = "loadout.traumas.hard.button.title";
        private const string _HARD_BUTTON_SUBTITLE_ = "loadout.traumas.hard.button.subtitle";
        
        private readonly ITimeGiver _timeService;
        private readonly CsvLocalization _localization;

        private Dictionary<int, TraumaTimerView> _instancedViews;

        public Action HealByBottleRequested;
        public Action HealByHardRequested;
        public Action ExitClicked;

        public LoadoutTraumasPresenter(ITimeGiver timeService, CsvLocalization localization)
        {
            _timeService = timeService;
            _localization = localization;
        }

        protected override void OnInited()
        {
            base.OnInited();

            View.Description.text = _localization.Get(_DESCRIPTION_KEY_);
            
            UpdateTimers(Model.Traumas);
            InitButtons(View);
        }

        private void InitButtons(LoadoutTraumasView view)
        {
            view.HealByBottleButton.SetText(_localization.Get(_BOTTLE_BUTTON_TITLE_));
            view.HealByBottleButton.SetSubText(_localization.Get(_BOTTLE_BUTTON_SUBTITLE_));
            view.HealByBottleButton.SetPrice(Model.BottleAsset, Model.BottlePrice);
            view.HealByBottleButton.Clicked += () => HealByBottleRequested?.Invoke();

            view.HealByHardButton.SetText(_localization.Get(_HARD_BUTTON_TITLE_));
            view.HealByHardButton.SetSubText(_localization.Get(_HARD_BUTTON_SUBTITLE_));
            view.HealByHardButton.SetPrice(CurrencyType.Hard, Model.HardHealPrice);
            view.HealByHardButton.Clicked += () => HealByHardRequested?.Invoke();

            view.ExitButton.Clicked += () => ExitClicked?.Invoke();
            view.BackButton.Clicked += () => ExitClicked?.Invoke();
        }

        public void UpdateTimers(List<PlayerTimer> playerTimers)
        {
            View.Cleanup();
            _instancedViews = new Dictionary<int, TraumaTimerView>();
            foreach (PlayerTimer trauma in playerTimers)
            {
                TraumaTimerView timerView = Object.Instantiate(View.TimerPrefab, View.TraumasRoot);
                timerView.SetTrauma(trauma.ItemId);
                timerView.Init(_timeService, _localization);
                timerView.SetTime((int)trauma.Duration, (int)trauma.EndTime);
                _instancedViews.Add(trauma.ItemId, timerView);
            }

            Model.Traumas = playerTimers;
            View.Title.text = string.Format(_localization.Get(_TITLE_KEY_), playerTimers.Count.ToString());
        }

        public void UpdateHardPrice(uint price)
        {
            Model.HardHealPrice = price;
            View.HealByHardButton.SetPrice(CurrencyType.Hard, Model.HardHealPrice);
        }

        public UniTask HealTrauma(int id)
        {
            return _instancedViews[id].PlayHeal();
        }

        public void Dispose()
        {
            if (View)
            {
                View.Cleanup();
                HealByBottleRequested = null;
                HealByHardRequested = null;
                ExitClicked = null;
            }
        }
    }
}