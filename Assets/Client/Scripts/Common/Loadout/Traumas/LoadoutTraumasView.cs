using Client.Common.Audio;
using Client.Common.Traumas.Views;
using Client.Common.UI.Buttons;
using Client.Common.UI.Buttons.Pay;
using Client.Utils.ViewService;
using TMPro;
using UnityEngine;

namespace Client.Common.Loadout.Traumas
{
    public class LoadoutTraumasView: View
    {
        public TraumaTimerView TimerPrefab;
        public TMP_Text Title;
        public TMP_Text Description;
        public Transform TraumasRoot;
        public ThePayButton HealByBottleButton;
        public ThePayButton HealByHardButton;
        
        public ActionButton ExitButton;
        public ActionButton BackButton;

        public void SetTopButton(bool back)
        {
            ExitButton.gameObject.SetActive(!back);
            BackButton.gameObject.SetActive(back);
        }
        
        public void Cleanup()
        {
            foreach (Transform t in TraumasRoot)
            {
                Destroy(t.gameObject);
            }
        }
    }
}