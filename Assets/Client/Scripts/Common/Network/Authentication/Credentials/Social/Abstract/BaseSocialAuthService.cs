using Client.Common.Analytics.Helpers;
using Client.Common.Analytics.Infrastructure.Events;
using Client.Common.Network.Connection.Reconnect.Systems;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using UnityEngine;

namespace Client.Common.Network.Authentication.Credentials.Social.Abstract
{
    public abstract class BaseSocialAuthService
    {
        protected readonly MetaNet.MetaNet MetaNet;
        protected readonly BaseSocialCredentialsProvider CredentialsProvider;
        private readonly NetworkReconnectController _networkReconnectController;
        private readonly SocialNetworkAnalyticsHelper _socialNetworkAnalytics;

        protected BaseSocialAuthService(
            MetaNet.MetaNet metaNet,
            BaseSocialCredentialsProvider credentialsProvider,
            NetworkReconnectController networkReconnectController, EcsWorld world)
        {
            MetaNet = metaNet;
            CredentialsProvider = credentialsProvider;
            _networkReconnectController = networkReconnectController;
            _socialNetworkAnalytics = new SocialNetworkAnalyticsHelper(world);
        }

        public static bool IsLoginProcessing { get; private set; }
        public bool IsTokenRequesting { get; private set; }

        public string Token => CredentialsProvider.Token;
        public bool IsLoggedIn => CredentialsProvider.HasCredentials;
    
        public async UniTask<bool> TryLogin()
        {
            if (IsLoginProcessing)
            {
                return false;
            }
            
            IsLoginProcessing = true;
            IsTokenRequesting = true;

            string token = await LoginAndReceiveToken();
            
            IsTokenRequesting = false;

            if (string.IsNullOrWhiteSpace(token))
            {
                _socialNetworkAnalytics.SendAnalyticsEvent(SocialNetworkLoginType.LoginResult, GetType().Name, SocialNetworkLoginResult.Failed);
                IsLoginProcessing = false;
                return false;
            }

            bool firstTimeBind = !await CredentialsProvider.IsUserExist(token);
            
            await Bind(token, firstTimeBind);
            _socialNetworkAnalytics.SendAnalyticsEvent(SocialNetworkLoginType.LoginResult, GetType().Name, SocialNetworkLoginResult.Success);

            if (!firstTimeBind)
            {
                _networkReconnectController.FullReconnect();
            }
            IsLoginProcessing = false;

            return true;
        }

        public async void Logout()
        {
            CredentialsProvider.RemoveToken();
            _socialNetworkAnalytics.SendAnalyticsEvent(SocialNetworkLoginType.LogoutResult, GetType().Name, SocialNetworkLoginResult.Success);
            _networkReconnectController.FullReconnect();
        }

#if DEBUG
        /// <summary>
        /// Totally remove social from account. For debug purpose only.
        /// </summary>
        public async UniTask Unbind(string token)
        {
            if (await CredentialsProvider.IsUserExist(token))
            {
                await UnbindFromServer(token);
                CredentialsProvider.RemoveToken();
            }
            else
            {
                Debug.LogWarning($"[{GetType().Name}] No user exists with ID: {token}");
            }
        }
#endif

        protected async UniTask Bind(string token, bool firstTimeBind)
        {
            if (firstTimeBind)
            {
                await BindToServer(token);
            }

            CredentialsProvider.SetToken(token);
        }

        protected abstract UniTask<string> LoginAndReceiveToken();
        protected abstract UniTask BindToServer(string token);
        protected abstract UniTask UnbindFromServer(string token);
    }
}