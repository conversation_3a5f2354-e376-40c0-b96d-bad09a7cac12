using Client.Common.Analytics.Infrastructure.Services;
using Client.Common.AppVersion;
using Client.Common.Network.Authentication.Credentials.Social.Abstract;
using Client.Common.Network.Authentication.Credentials.Social.Helpers;
using Client.Common.Network.Authentication.Helpers;
using Client.Common.Network.Http;
using Client.Common.SceneLoading.Analytics;
using Client.Utils.ServiceTool;
using Cysharp.Threading.Tasks;

namespace Client.Common.Network.Authentication.Credentials.Social.Google
{
    public class GoogleCredentialsProvider : BaseSocialCredentialsProvider
    {
        public GoogleCredentialsProvider(HttpAuthenticator httpAuthenticator) 
            : base(httpAuthenticator, storageKey: "googleId")
        {
            AnalyticsService analyticsService = Service<AnalyticsService>.Get();
            GoogleLoginAnalyticsTracker tracker = new(analyticsService);
            if (CheckOldTokenType(Token))
            {
                tracker.Track();
                Token = string.Empty;
            }
        }
    
        protected override async UniTask<string> GetAuthUri(string token)
        {
            string appFullVersion = (await AppVersionProvider.GetClientVersion()).FullVersion;
            return $"{HttpNet.Host}/auth?googleId={token}&version={appFullVersion}";
        }

        private bool CheckOldTokenType(string token)
        {
            if (token == null)
            {
                return false;
            }
            
            (bool isValid, string[] _) tokenData = JwtTokenHelper.CheckValidJwtToken(token);
            return tokenData.isValid;
        }
    }}