using System;
using System.Text;
using Client.Common.Network.Authentication.Credentials.Social.Google;
using Client.Utils.GameLogger;
using Newtonsoft.Json;

namespace Client.Common.Network.Authentication.Credentials.Social.Helpers
{
    public static class JwtTokenHelper
    {
        public static string DecodeGoogleId(string jwtToken)
        {
            (bool isValid, string[] parts) tokenData = CheckValidJwtToken(jwtToken);
            if (!tokenData.isValid)
            {
                return null;
            }   

            string payload = Base64UrlDecode(tokenData.parts[1]); 
            GoogleJwtPayload googleId = JsonConvert.DeserializeObject<GoogleJwtPayload>(payload);;
            GameLogger.Log($"GoogleId Sub: {googleId.Sub}");
            return googleId.Sub;
        }
        
        private static string Base64UrlDecode(string input)
        {
            string replaced = input.Replace('_', '/').Replace('-', '+');
            switch (input.Length % 4)
            {
                case 2: replaced += "=="; break;
                case 3: replaced += "="; break;
            }
            byte[] bytes = Convert.FromBase64String(replaced);
            return Encoding.UTF8.GetString(bytes);
        }

        public static (bool, string[]) CheckValidJwtToken(string jwtToken)
        {
            string[] parts = jwtToken.Split('.');
            if (parts.Length != 3)
            {
                GameLogger.LogError("Invalid JWT format: Expected 3 parts.");
                return (false, null);
            }

            return (true, parts);
        }
    }
}