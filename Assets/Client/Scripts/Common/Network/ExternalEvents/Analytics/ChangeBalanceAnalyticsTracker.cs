using System;
using System.Collections.Generic;
using Client.Common.Analytics.Infrastructure.Events;
using Client.Common.Network.ExternalEvents.EventReceivers;
using Client.Common.Player.Controllers;
using Client.Utils.Abstract;
using External;
using Leopotam.Ecs;

namespace Client.Common.Network.ExternalEvents.Analytics
{
    public class ChangeBalanceAnalyticsTracker : IInitable, IDisposable
    {
        private const string _GET_EVENT_ID_ = "balance_main_get";
        private const string _SPEND_EVENT_ID_ = "balance_main_spend";

        private readonly EcsWorld _world;
        private readonly PlayerManager _playerManager;
        private readonly IExternalEventReceiver<ChangeMainBalance> _receiver;

        public ChangeBalanceAnalyticsTracker(EcsWorld world, PlayerManager playerManager, IExternalEventReceiver<ChangeMainBalance> receiver)
        {
            _world = world;
            _playerManager = playerManager;
            _receiver = receiver;
        }

        public void Init()
        {
            _receiver.Received += Track;
        }

        private void Track(ChangeMainBalance dataHolder)
        {
            TrackGet(dataHolder);
            TrackSpend(dataHolder);
        }

        private void TrackGet(ChangeMainBalance dataHolder)
        {
            foreach (ChangeMainBalanceItem changeBalance in dataHolder.Get)
            {
                FireEvent(_GET_EVENT_ID_, changeBalance);
            }
        }

        private void TrackSpend(ChangeMainBalance dataHolder)
        {
            foreach (ChangeMainBalanceItem changeBalance in dataHolder.Spend)
            {
                FireEvent(_SPEND_EVENT_ID_, changeBalance);
            }
        }

        private void FireEvent(string eventId, ChangeMainBalanceItem data)
        {
            EcsEntity analyticsEvent = _world.NewEntity();
            analyticsEvent.Get<AnalyticsCustomEvent>().Id = eventId;
            analyticsEvent.Get<AnalyticsParameters>().Parameters = new Dictionary<string, string>()
            {
                ["Type"] = data.Type.ToString(),
                ["Item"] = data.ItemId.ToString(),
                ["Count"] = data.Count.ToString(),
                ["Balances"] = data.Balance.ToString(),
                ["Rank"] = _playerManager.Session.Progress.Rank.ToString(),
            };
        }

        public void Dispose()
        {
            _receiver.Received -= Track;
        }
    }
}