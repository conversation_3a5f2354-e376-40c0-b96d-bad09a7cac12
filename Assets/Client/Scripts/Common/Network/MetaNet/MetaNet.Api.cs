using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Client.Common.Abilities.Infrastructure.Data;
using Client.Common.Bank.Controllers;
using Client.Common.GameSettings;
using Client.Common.Network.Proto;
using Client.Common.Network.RequestHandlers;
using Client.Common.Network.Utils;
using Client.Common.Network.WebSockets;
using Client.Utils.FireBase;
using Client.Utils.GameLogger;
using Client.Utils.ResultTool.Results;
using Client.Utils.SimpleBinary;
using Common;
using Cysharp.Threading.Tasks;
using External;
using Google.Protobuf;
using EmptyMessage = Google.Protobuf.WellKnownTypes.Empty;
using ProtoCurrency = Common.CurrencyType;

namespace Client.Common.Network.MetaNet
{
    public sealed class MetaNet
    {
        private readonly WebSocketClient _webSocket;
        private readonly IRequestHandler _handler;

        public MetaNet(WebSocketClient webSocketClient, IRequestHandler handler)
        {
            _webSocket = webSocketClient;
            _handler = handler;
        }

        public UniTask<Result<ClientInventoryResponse>> GetInventory(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientInventoryResponse>> Request(CancellationToken token)
            {
                ClientInventoryRequest request = ClientInventoryRequest.New();

                SbsWrapper<ClientInventoryRequest, ClientInventoryResponse> wrapper = new(
                    serialize: (ClientInventoryRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientInventoryResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result> AddItemsToInventory(IEnumerable<ItemComponent> items, CancellationToken token = default)
        {
            return _handler.Handle(Request, token).AsResult();

            UniTask<Result<EmptyMessage>> Request(CancellationToken token)
            {
                ExternalAddInventoryRequest request = new();

                foreach (ItemComponent item in items)
                {
                    request.Item.Add(item);    
                }
                
                return _webSocket.SendProtoAsync(request, EmptyMessage.Parser, ExternalRequestType.AddInventory, token);
            }
        }

        public UniTask<Result> AddItemToInventory(ItemComponent item, CancellationToken token = default)
        {
            return _handler.Handle(Request, token).AsResult();

            UniTask<Result<EmptyMessage>> Request(CancellationToken token)
            {
                ExternalAddInventoryRequest request = new();

                request.Item.Add(item);    
                
                return _webSocket.SendProtoAsync(request, EmptyMessage.Parser, ExternalRequestType.AddInventory, token);
            }
        }
        
        public UniTask<Result> RemoveItemsFromInventory(IEnumerable<ItemComponent> items, CancellationToken token = default)
        {
            return _handler.Handle(Request, token).AsResult();

            UniTask<Result<EmptyMessage>> Request(CancellationToken token)
            {
                ExternalAddInventoryRequest request = new();

                foreach (ItemComponent item in items)
                {
                    request.Item.Add(item);    
                }

                return _webSocket.SendProtoAsync(request, EmptyMessage.Parser, ExternalRequestType.RemoveInventory, token);
            }
        }

        public UniTask<Result> RemoveItemFromInventory(ItemComponent item, CancellationToken token = default)
        {
            return _handler.Handle(Request, token).AsResult();

            UniTask<Result<EmptyMessage>> Request(CancellationToken token)
            {
                ExternalAddInventoryRequest request = new();

                request.Item.Add(item);

                return _webSocket.SendProtoAsync(request, EmptyMessage.Parser, ExternalRequestType.RemoveInventory, token);
            }
        }

        public async UniTask<Result> SpendCurrency(
            ProtoCurrency currency,
            int count = 1,
            CancellationToken token = default)
        {
            int itemId = currency.GetItemIdByCurrencyType();

            ItemComponent item = new()
            {
                Id = itemId,
                Count = count
            };
            
            Result result = await RemoveItemFromInventory(item, token);

            if (result.IsFailure)
            {
                GameLogger.LogError($"Failed to spend currency {itemId}");
            }

            return result;
        }

        public UniTask<Result<ClientItemDataResponse>> GetItemData(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientItemDataResponse>> Request(CancellationToken token)
            {
                ClientItemDataRequest request = ClientItemDataRequest.New();

                SbsWrapper<ClientItemDataRequest, ClientItemDataResponse> wrapper = new(
                    serialize: (ClientItemDataRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientItemDataResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ExternalGetGuideResponse>> GetServerGuide(GuideType guideType, CancellationToken cancellationToken)
        {
            return _handler.Handle(Request, cancellationToken);

            UniTask<Result<ExternalGetGuideResponse>> Request(CancellationToken token)
            {
                ExternalGetGuideRequest request = new()
                {
                    Type = guideType
                };

                return _webSocket.SendProtoAsync(request, ExternalGetGuideResponse.Parser, ExternalRequestType.GetGuide, token);
            }
        }

        public UniTask<Result<T>> GetServerGuide<T>(GuideType guideType, CancellationToken cancellationToken) where T : class, IMessage<T>, new()
        {
            return _handler.Handle(Request, cancellationToken);

            UniTask<Result<T>> Request(CancellationToken token)
            {
                ExternalGetGuideRequest request = new()
                {
                    Type = guideType
                };

                return _webSocket.SendProtoAsync(request, new MessageParser<T>(() => new T()), ExternalRequestType.GetGuide, token);
            }
        }

        public UniTask<Result<ClientPrefResponse>> GetServerPreferences(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientPrefResponse>> Request(CancellationToken token)
            {
                ClientPrefRequest request = ClientPrefRequest.New();

                SbsWrapper<ClientPrefRequest, ClientPrefResponse> wrapper = new(
                    serialize: (ClientPrefRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientPrefResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientNicknameResponse>> GetNickname(string newNickname = "", CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientNicknameResponse>> Request(CancellationToken token)
            {
                ClientNicknameRequest request = ClientNicknameRequest.New();
                request.Nickname = newNickname ?? "";

                SbsWrapper<ClientNicknameRequest, ClientNicknameResponse> wrapper = new(
                    serialize: (ClientNicknameRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientNicknameResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ExternalGetProgressResponse>> GetProgress(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ExternalGetProgressResponse>> Request(CancellationToken token)
            {
                UserRequest request = new();

                return _webSocket.SendProtoAsync(request, ExternalGetProgressResponse.Parser, ExternalRequestType.GetProgress, token);
            }
        }

        public UniTask<Result<ClientTimerResponse>> GetTimers(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientTimerResponse>> Request(CancellationToken token)
            {
                ClientTimerRequest request = ClientTimerRequest.New();

                SbsWrapper<ClientTimerRequest, ClientTimerResponse> wrapper = new(
                    serialize: (ClientTimerRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientTimerResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientItemEquipResponse>> EquipItem(int itemId, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientItemEquipResponse>> Request(CancellationToken token)
            {
                ClientItemEquipRequest request = ClientItemEquipRequest.New();
                request.ItemId = itemId.ToString();

                SbsWrapper<ClientItemEquipRequest, ClientItemEquipResponse> wrapper = new(
                    serialize: (ClientItemEquipRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientItemEquipResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientChestOpenResponse>> OpenChest(
            int chestId,
            int lockpickId,
            CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientChestOpenResponse>> Request(CancellationToken token)
            {
                ClientChestOpenRequest request = ClientChestOpenRequest.New();
                request.ChestId = (ushort) chestId;
                request.LockpickId = (ushort) lockpickId;

                SbsWrapper<ClientChestOpenRequest, ClientChestOpenResponse> wrapper = new(
                    serialize: (ClientChestOpenRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientChestOpenResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientBattleEnterResponse>> EnterBattle(BattleOpponent battleOpponent, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientBattleEnterResponse>> Request(CancellationToken token)
            {
                ClientBattleEnterRequest request = ClientBattleEnterRequest.New();
                request.Opponent = battleOpponent;

                SbsWrapper<ClientBattleEnterRequest, ClientBattleEnterResponse> wrapper = new(
                    serialize: (ClientBattleEnterRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientBattleEnterResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientRegionLeaveResponse>> LeaveRegion(bool lose, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientRegionLeaveResponse>> Request(CancellationToken token)
            {
                ClientRegionLeaveRequest request = ClientRegionLeaveRequest.New();
                request.Lose = (byte) (lose ? 1 : 0);

                SbsWrapper<ClientRegionLeaveRequest, ClientRegionLeaveResponse> wrapper = new(
                    serialize: (ClientRegionLeaveRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientRegionLeaveResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientRegionReviveResponse>> ReviveAtRegion(CurrencyType paidType, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientRegionReviveResponse>> Request(CancellationToken token)
            {
                ClientRegionReviveRequest request = ClientRegionReviveRequest.New();
                request.Paid = paidType;

                SbsWrapper<ClientRegionReviveRequest, ClientRegionReviveResponse> wrapper = new(
                    serialize: (ClientRegionReviveRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientRegionReviveResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientTraumaAddResponse>> AddTrauma(BodyPart bodyPart, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientTraumaAddResponse>> Request(CancellationToken token)
            {
                ClientTraumaAddRequest request = ClientTraumaAddRequest.New();
                request.Part = bodyPart;

                SbsWrapper<ClientTraumaAddRequest, ClientTraumaAddResponse> wrapper = new(
                    serialize: (ClientTraumaAddRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientTraumaAddResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientItemBuyResponse>> BuyItem(
            int itemId,
            int itemCount = 1,
            CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientItemBuyResponse>> Request(CancellationToken token)
            {
                ClientItemBuyRequest request = ClientItemBuyRequest.New();
                request.ItemId = itemId.ToString();
                request.Count = (ushort) itemCount;

                SbsWrapper<ClientItemBuyRequest, ClientItemBuyResponse> wrapper = new(
                    serialize: (ClientItemBuyRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientItemBuyResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientCraftItemResponse>> Craft(int itemId, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientCraftItemResponse>> Request(CancellationToken token)
            {
                ClientCraftItemRequest request = ClientCraftItemRequest.New();
                request.ItemId = (ushort) itemId;

                SbsWrapper<ClientCraftItemRequest, ClientCraftItemResponse> wrapper = new(
                    serialize: (ClientCraftItemRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientCraftItemResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientForceCraftItemResponse>> ForceCraft(
            int itemId,
            ForceCraftItemType craftItemType,
            CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientForceCraftItemResponse>> Request(CancellationToken token)
            {
                ClientForceCraftItemRequest request = ClientForceCraftItemRequest.New();
                request.ItemId = (ushort) itemId;
                request.Type = craftItemType;

                SbsWrapper<ClientForceCraftItemRequest, ClientForceCraftItemResponse> wrapper = new(
                    serialize: (ClientForceCraftItemRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientForceCraftItemResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientCraftedItemsResponse>> GetCraftedItems(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientCraftedItemsResponse>> Request(CancellationToken token)
            {
                ClientCraftedItemsRequest request = ClientCraftedItemsRequest.New();

                SbsWrapper<ClientCraftedItemsRequest, ClientCraftedItemsResponse> wrapper = new(
                    serialize: (ClientCraftedItemsRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientCraftedItemsResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientTakeCraftedItemResponse>> TakeCraftedItem(int itemId, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientTakeCraftedItemResponse>> Request(CancellationToken token)
            {
                ClientTakeCraftedItemRequest request = ClientTakeCraftedItemRequest.New();
                request.ItemId = (ushort) itemId;

                SbsWrapper<ClientTakeCraftedItemRequest, ClientTakeCraftedItemResponse> wrapper = new(
                    serialize: (ClientTakeCraftedItemRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientTakeCraftedItemResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientBattleResultResponse>> BattleResult(
            string battleResultId,
            BattleResult battleResult,
            BattleLocation battleLocation,
            BattleOpponent battleOpponent,
            List<SkinDurability> skinDurabilities,
            int playerHp,
            CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientBattleResultResponse>> Request(CancellationToken token)
            {
                ClientBattleResultRequest request = ClientBattleResultRequest.New();

                request.Battle = new Battle
                {
                    Id = battleResultId,
                    State = BattleState.Finished,
                    Result = battleResult,
                    Location = battleLocation,
                    Opponent = battleOpponent,
                    SkinDurabilities = skinDurabilities,
                    Hp = (ushort) playerHp
                };

                SbsWrapper<ClientBattleResultRequest, ClientBattleResultResponse> wrapper = new(
                    serialize: (ClientBattleResultRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientBattleResultResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<LootResponse>> GetLootTreasure(ushort itemId, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<LootResponse>> Request(CancellationToken token)
            {
                LootRequest request = LootRequest.New();
                request.LootId = itemId;

                SbsWrapper<LootRequest, LootResponse> wrapper = new(
                    serialize: (LootRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => LootResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientTopResponse>> ClientGlobalsTop(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientTopResponse>> Request(CancellationToken token)
            {
                ClientTopRequest request = ClientTopRequest.New();

                SbsWrapper<ClientTopRequest, ClientTopResponse> wrapper = new(
                    serialize: (ClientTopRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientTopResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientTournamentInfoResponse>> TournamentInfo(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientTournamentInfoResponse>> Request(CancellationToken token)
            {
                ClientTournamentInfoRequest request = ClientTournamentInfoRequest.New();

                SbsWrapper<ClientTournamentInfoRequest, ClientTournamentInfoResponse> wrapper = new(
                    serialize: (ClientTournamentInfoRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientTournamentInfoResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientTournamentAwardsResponse>> TournamentAwards(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientTournamentAwardsResponse>> Request(CancellationToken token)
            {
                ClientTournamentAwardsRequest request = ClientTournamentAwardsRequest.New();

                SbsWrapper<ClientTournamentAwardsRequest, ClientTournamentAwardsResponse> wrapper = new(
                    serialize: (ClientTournamentAwardsRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientTournamentAwardsResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientUserTournamentAwardResponse>> GetClientUserTournamentAward(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientUserTournamentAwardResponse>> Request(CancellationToken token)
            {
                ClientUserTournamentAwardRequest request = ClientUserTournamentAwardRequest.New();
                request.Action = UserTournamentAwardAction.Get;

                SbsWrapper<ClientUserTournamentAwardRequest, ClientUserTournamentAwardResponse> wrapper = new(
                    serialize: (ClientUserTournamentAwardRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientUserTournamentAwardResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientUserTournamentAwardResponse>> RemoveClientUserTournamentAward(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientUserTournamentAwardResponse>> Request(CancellationToken token)
            {
                ClientUserTournamentAwardRequest request = ClientUserTournamentAwardRequest.New();
                request.Action = UserTournamentAwardAction.Remove;

                SbsWrapper<ClientUserTournamentAwardRequest, ClientUserTournamentAwardResponse> wrapper = new(
                    serialize: (ClientUserTournamentAwardRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientUserTournamentAwardResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientKillLocalInfoResponse>> GetLocationsKillsProgress(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientKillLocalInfoResponse>> Request(CancellationToken token)
            {
                ClientKillLocalInfoRequest request = ClientKillLocalInfoRequest.New();

                SbsWrapper<ClientKillLocalInfoRequest, ClientKillLocalInfoResponse> wrapper = new(
                    serialize: (ClientKillLocalInfoRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientKillLocalInfoResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientKillLocalAwardResponse>> ActivateLocationsKillProgressRewards(
            int regionId,
            int locationId,
            CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientKillLocalAwardResponse>> Request(CancellationToken token)
            {
                ClientKillLocalAwardRequest request = ClientKillLocalAwardRequest.New();
                request.RegionId = (byte) regionId;
                request.LocationId = (byte) locationId;

                SbsWrapper<ClientKillLocalAwardRequest, ClientKillLocalAwardResponse> wrapper = new(
                    serialize: (ClientKillLocalAwardRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientKillLocalAwardResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientPurchaseListResponse>> GetPurchaseList(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientPurchaseListResponse>> Request(CancellationToken token)
            {
                ClientPurchaseListRequest request = ClientPurchaseListRequest.New();

                SbsWrapper<ClientPurchaseListRequest, ClientPurchaseListResponse> wrapper = new(
                    serialize: (ClientPurchaseListRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientPurchaseListResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ClientCreatePurchaseResponse>> CreatePurchase(
            string storeId,
            GatewayType gatewayType,
            CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientCreatePurchaseResponse>> Request(CancellationToken token)
            {
                ClientCreatePurchaseRequest request = ClientCreatePurchaseRequest.New();
                request.StoreId = storeId;
                request.Gateway = gatewayType;

                SbsWrapper<ClientCreatePurchaseRequest, ClientCreatePurchaseResponse> wrapper = new(
                    serialize: (ClientCreatePurchaseRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientCreatePurchaseResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ConfirmPurchaseResponse>> ConfirmPurchase(
            string orderId,
            string details,
            CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ConfirmPurchaseResponse>> Request(CancellationToken token)
            {
                ConfirmPurchaseRequest request = ConfirmPurchaseRequest.New();
                request.Order = orderId;
                request.Details = details;

                SbsWrapper<ConfirmPurchaseRequest, ConfirmPurchaseResponse> wrapper = new(
                    serialize: (ConfirmPurchaseRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ConfirmPurchaseResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ServerTimeResponse>> GetServerTime(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ServerTimeResponse>> Request(CancellationToken token)
            {
                ServerTimeRequest request = ServerTimeRequest.New();

                SbsWrapper<ServerTimeRequest, ServerTimeResponse> wrapper = new(
                    serialize: (ServerTimeRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ServerTimeResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<FeedbackResponse>> SendFeedback(
            string comment,
            byte rating,
            Platform platform,
            string created,
            CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<FeedbackResponse>> Request(CancellationToken token)
            {
                FeedbackRequest request = FeedbackRequest.New();
                request.Comment = comment;
                request.Rating = rating;
                request.Platform = platform;
                request.Created = created;

                SbsWrapper<FeedbackRequest, FeedbackResponse> wrapper = new(
                    serialize: (FeedbackRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => FeedbackResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<HealResponse>> HealBodyPart(BodyPart bodyPart, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<HealResponse>> Request(CancellationToken token)
            {
                HealRequest request = HealRequest.New();
                request.Part = bodyPart;

                SbsWrapper<HealRequest, HealResponse> wrapper = new(
                    serialize: (HealRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => HealResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<BattleProgressResponse>> GetBattleProgress(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<BattleProgressResponse>> Request(CancellationToken token)
            {
                BattleProgressRequest request = BattleProgressRequest.New();

                SbsWrapper<BattleProgressRequest, BattleProgressResponse> wrapper = new(
                    serialize: (BattleProgressRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => BattleProgressResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        #region Quests

        public UniTask<Result<QuestsInfoResponse>> GetQuestDefs(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<QuestsInfoResponse>> Request(CancellationToken token)
            {
                QuestsInfoRequest request = QuestsInfoRequest.New();
                request.Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();

                SbsWrapper<QuestsInfoRequest, QuestsInfoResponse> wrapper = new(
                    serialize: (QuestsInfoRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => QuestsInfoResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<QuestsStateResponse>> GetQuestState(QuestType type = QuestType.Unknown, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<QuestsStateResponse>> Request(CancellationToken token)
            {
                QuestsStateRequest request = QuestsStateRequest.New();
                request.Type = type;

                SbsWrapper<QuestsStateRequest, QuestsStateResponse> wrapper = new(
                    serialize: (QuestsStateRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => QuestsStateResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<QuestsProgressResponse>> SetQuestsProgress(List<UserQuest> quests, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<QuestsProgressResponse>> Request(CancellationToken token)
            {
                QuestsProgressRequest request = QuestsProgressRequest.New();
                request.Quests = quests;

                SbsWrapper<QuestsProgressRequest, QuestsProgressResponse> wrapper = new(
                    serialize: (QuestsProgressRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => QuestsProgressResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<QuestsRewardResponse>> TakeQuestRewardAsync(
            ushort questId,
            QuestRewardMultiplier multiplier = QuestRewardMultiplier.Default,
            CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<QuestsRewardResponse>> Request(CancellationToken token)
            {
                QuestRewardRequest request = QuestRewardRequest.New();
                request.QuestId = questId;
                request.Multiplier = multiplier;

                SbsWrapper<QuestRewardRequest, QuestsRewardResponse> wrapper = new(
                    serialize: (QuestRewardRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => QuestsRewardResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        #endregion

        public UniTask<Result<StatusResponse>> AdminProgressAction(
            AdminProgressAction action,
            int itemId,
            uint count,
            CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<StatusResponse>> Request(CancellationToken token)
            {
                AdminProgressRequest request = AdminProgressRequest.New();
                request.Action = action;
                ItemQuantity itemQuantity = ItemQuantity.New();
                itemQuantity.Id = itemId;
                itemQuantity.Count = count;
                request.Item = itemQuantity;

                SbsWrapper<AdminProgressRequest, StatusResponse> wrapper = new(
                    serialize: (AdminProgressRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => StatusResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<DrainBloodResponse>> DrainBlood(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<DrainBloodResponse>> Request(CancellationToken token)
            {
                DrainBloodRequest request = DrainBloodRequest.New();

                SbsWrapper<DrainBloodRequest, DrainBloodResponse> wrapper = new(
                    serialize: (DrainBloodRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => DrainBloodResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<CollectBloodResponse>> CollectBlood(ushort itemId = 1, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<CollectBloodResponse>> Request(CancellationToken token)
            {
                CollectBloodRequest request = CollectBloodRequest.New();
                request.ItemId = itemId;

                SbsWrapper<CollectBloodRequest, CollectBloodResponse> wrapper = new(
                    serialize: (CollectBloodRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => CollectBloodResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<StatusResponse>> SetBloodBottleMaxSize(ushort maxSize, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<StatusResponse>> Request(CancellationToken token)
            {
                SetBloodBottleMaxSizeRequest request = SetBloodBottleMaxSizeRequest.New();
                request.MaxSize = maxSize;

                SbsWrapper<SetBloodBottleMaxSizeRequest, StatusResponse> wrapper = new(
                    serialize: (SetBloodBottleMaxSizeRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => StatusResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<StatusResponse>> ResetBloodBottleMaxSize(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<StatusResponse>> Request(CancellationToken token)
            {
                ResetBloodBottleMaxSizeRequest request = ResetBloodBottleMaxSizeRequest.New();

                SbsWrapper<ResetBloodBottleMaxSizeRequest, StatusResponse> wrapper = new(
                    serialize: (ResetBloodBottleMaxSizeRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => StatusResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<StatusResponse>> SendFtue(string stages, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<StatusResponse>> Request(CancellationToken token)
            {
                StageProgressRequest request = StageProgressRequest.New();
                request.Progress = stages;

                SbsWrapper<StageProgressRequest, StatusResponse> wrapper = new(
                    serialize: (StageProgressRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => StatusResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<StatusResponse>> SetRankAdmin(byte newRank, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<StatusResponse>> Request(CancellationToken token)
            {
                AdminChangeRankRequest request = AdminChangeRankRequest.New();
                request.Rank = newRank;

                SbsWrapper<AdminChangeRankRequest, StatusResponse> wrapper = new(
                    serialize: (AdminChangeRankRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => StatusResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<OpenMultiLootBoxResponse>> GetMultiLoot(ushort itemId, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<OpenMultiLootBoxResponse>> Request(CancellationToken token)
            {
                OpenMultiLootBoxRequest request = OpenMultiLootBoxRequest.New();
                request.Item = itemId;

                SbsWrapper<OpenMultiLootBoxRequest, OpenMultiLootBoxResponse> wrapper = new(
                    serialize: (OpenMultiLootBoxRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => OpenMultiLootBoxResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ChangeTimerResponse>> ChangeTimer(
            TimerType timerType,
            int timerId,
            int millisecs,
            CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ChangeTimerResponse>> Request(CancellationToken token)
            {
                ChangeTimerRequest request = ChangeTimerRequest.New();
                request.Type = timerType;
                request.Item = (ushort) timerId;
                request.Diff = millisecs;

                SbsWrapper<ChangeTimerRequest, ChangeTimerResponse> wrapper = new(
                    serialize: (ChangeTimerRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ChangeTimerResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<ExternalGetUserSettingsResponse>> GetSettings(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ExternalGetUserSettingsResponse>> Request(CancellationToken token)
            {
                EmptyMessage request = new();

                return _webSocket.SendProtoAsync(request, ExternalGetUserSettingsResponse.Parser, ExternalRequestType.GetUserSettings, token);
            }
        }

        public UniTask<Result<ChangeSettingsResponse>> SetSettings(GameSettingsData saveSettings, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ChangeSettingsResponse>> Request(CancellationToken token)
            {
                ChangeSettingsRequest request = ChangeSettingsRequest.New();
                request.Sound = (byte) (saveSettings.SoundEffectsToggleState ? 1 : 0);
                request.Music = (byte) (saveSettings.MusicToggleState ? 1 : 0);
                request.Vibration = (byte) (saveSettings.VibrationsToggleState ? 1 : 0);
                request.Language = saveSettings.LanguageState;
                request.Country = saveSettings.CountryState;
                request.RightHandedControl = (byte) (saveSettings.InvertedInputState ? 1 : 0);
                request.Notifications = GetMetanetNotifications();

                SbsWrapper<ChangeSettingsRequest, ChangeSettingsResponse> wrapper = new(
                    serialize: (ChangeSettingsRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ChangeSettingsResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }

            List<NotificationSettings> GetMetanetNotifications()
            {
                return saveSettings.NotificationSettings.Select(
                                       setting =>
                                       {
                                           if (setting == null)
                                           {
                                               try
                                               {
                                                   throw new CrashlyticsException("MetaNet SetSettings saveSettings.NotificationSettings setting is null");
                                               }
                                               catch (Exception e)
                                               {
                                                   GameLogger.LogErrorException(e.Message, "MetaNetSaveSettingsNotificationSettings");

                                                   return default;
                                               }
                                           }

                                           return new NotificationSettings()
                                           {
                                               Enabled = (byte) (setting.Enabled ? 1 : 0),
                                               NotificationType = setting.NotificationType
                                           };
                                       })
                                   .ToList();
            }
        }

        public UniTask<Result<ClientRegionEnterResponse>> EnterRegion(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ClientRegionEnterResponse>> Request(CancellationToken token)
            {
                ClientRegionEnterRequest request = ClientRegionEnterRequest.New();

                SbsWrapper<ClientRegionEnterRequest, ClientRegionEnterResponse> wrapper = new(
                    serialize: (ClientRegionEnterRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => ClientRegionEnterResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<BloodChunkResponse>> CheckBloodChunk(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<BloodChunkResponse>> Request(CancellationToken token)
            {
                BloodChunkRequest request = BloodChunkRequest.New();

                SbsWrapper<BloodChunkRequest, BloodChunkResponse> wrapper = new(
                    serialize: (BloodChunkRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => BloodChunkResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result<StatusResponse>> SetDurabilityDebug(List<SkinDurability> skinDurabilities, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<StatusResponse>> Request(CancellationToken token)
            {
                AdminSkinDurabilitiesRequest request = AdminSkinDurabilitiesRequest.New();
                request.SkinDurabilities = skinDurabilities;

                SbsWrapper<AdminSkinDurabilitiesRequest, StatusResponse> wrapper = new(
                    serialize: (AdminSkinDurabilitiesRequest r, ref SimpleBinarySerializer sbs) => r.Serialize(ref sbs),
                    recycle: r => r.Recycle(),
                    deserialize: (ref SimpleBinarySerializer sbs) => StatusResponse.Deserialize(ref sbs)
                );

                return _webSocket.SendSbsAsync(request, wrapper, token);
            }
        }

        public UniTask<Result> BindFacebook(string socialToken, CancellationToken token = default)
        {
            return _handler.Handle(Request, token).AsResult();

            UniTask<Result<EmptyMessage>> Request(CancellationToken token)
            {
                ExternalBindSocialRequest request = new()
                {
                    Token = socialToken
                };

                return _webSocket.SendProtoAsync(request, EmptyMessage.Parser, ExternalRequestType.BindFacebook, token);
            }
        }

        public UniTask<Result> UnbindFacebook(string socialToken, CancellationToken token = default)
        {
            return _handler.Handle(Request, token).AsResult();

            UniTask<Result<EmptyMessage>> Request(CancellationToken token)
            {
                ExternalBindSocialRequest request = new()
                {
                    Token = socialToken
                };

                return _webSocket.SendProtoAsync(request, EmptyMessage.Parser, ExternalRequestType.UnbindFacebook, token);
            }
        }

        public UniTask<Result> BindGoogle(string socialToken, CancellationToken token = default)
        {
            return _handler.Handle(Request, token).AsResult();

            UniTask<Result<EmptyMessage>> Request(CancellationToken token)
            {
                ExternalBindSocialRequest request = new()
                {
                    Token = socialToken
                };

                return _webSocket.SendProtoAsync(request, EmptyMessage.Parser, ExternalRequestType.BindGoogle, token);
            }
        }

        public UniTask<Result> UnbindGoogle(string socialToken, CancellationToken token = default)
        {
            return _handler.Handle(Request, token).AsResult();

            UniTask<Result<EmptyMessage>> Request(CancellationToken token)
            {
                ExternalBindSocialRequest request = new()
                {
                    Token = socialToken
                };

                return _webSocket.SendProtoAsync(request, EmptyMessage.Parser, ExternalRequestType.UnbindGoogle, token);
            }
        }

        public UniTask<Result> SetOutfitDurability(
            int itemId,
            int durability,
            CancellationToken token = default)
        {
            return _handler.Handle(Request, token).AsResult();

            UniTask<Result<EmptyMessage>> Request(CancellationToken token)
            {
                External.SkinDurability skinDurability = new()
                {
                    Id = itemId,
                    Durability = durability
                };

                ExternalSetSkinDurabilitiesRequest request = new()
                {
                    SkinDurabilities = {skinDurability}
                };

                return _webSocket.SendProtoAsync(request, EmptyMessage.Parser, ExternalRequestType.SetSkinDurabilities, token);
            }
        }

        public UniTask<Result> SetWeaponSkinsDurability(External.WeaponSkin weaponSkin, CancellationToken token = default)
        {
            return _handler.Handle(Request, token).AsResult();

            UniTask<Result<EmptyMessage>> Request(CancellationToken token)
            {
                ExternalSetWeaponSkinsRequest request = new()
                {
                    WeaponSkins = {weaponSkin}
                };

                return _webSocket.SendProtoAsync(request, EmptyMessage.Parser, ExternalRequestType.SetWeaponSkins, token);
            }
        }

        public UniTask<Result> EquipWeaponSkin(
            int itemId,
            int skinId,
            CancellationToken token = default)
        {
            return _handler.Handle(Request, token).AsResult();

            UniTask<Result<EmptyMessage>> Request(CancellationToken token)
            {
                ExternalEquipWeaponSkinRequest request = new()
                {
                    WeaponId = itemId,
                    SkinId = skinId,
                };

                return _webSocket.SendProtoAsync(request, EmptyMessage.Parser, ExternalRequestType.EquipWeaponSkin, token);
            }
        }

        public UniTask<Result<ExternalLootCorpseResponse>> GetLootCorpse(int rankId, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ExternalLootCorpseResponse>> Request(CancellationToken token)
            {
                ExternalLootCorpseRequest request = new()
                {
                    CorpseRank = rankId
                };

                return _webSocket.SendProtoAsync(request, ExternalLootCorpseResponse.Parser, ExternalRequestType.LootCorpse, token);
            }
        }

        public UniTask<Result> SetAccountSteps(IEnumerable<AccountStep> steps, CancellationToken token = default)
        {
            return _handler.Handle(Request, token).AsResult();

            UniTask<Result<EmptyMessage>> Request(CancellationToken token)
            {
                ExternalUpdateAccountStepsRequest request = new()
                {
                    Steps = {steps}
                };

                return _webSocket.SendProtoAsync(request, EmptyMessage.Parser, ExternalRequestType.UpdateAccountSteps, token);
            }
        }

        public UniTask<Result<ExternalGetPurchasesResponse>> GetBankProducts(Dictionary<string, DateTime> firstEncounterTimeMap, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ExternalGetPurchasesResponse>> Request(CancellationToken token)
            {
                List<FirstTimeSeenPurchase> firstTimeSeenPurchases = new FirstEncounterMapToListConverter().Convert(firstEncounterTimeMap);
                ExternalGetPurchasesRequest request = new();
                request.FirstTimeSeenPurchase.AddRange(firstTimeSeenPurchases);

                return _webSocket.SendProtoAsync(request, ExternalGetPurchasesResponse.Parser, ExternalRequestType.GetPurchases, token);
            }
        }

        public UniTask<Result<ExternalAccountAbilityResponse>> GetAbilityGrade(AbilityId abilityId, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ExternalAccountAbilityResponse>> Request(CancellationToken token)
            {
                ExternalAccountAbilityRequest request = new()
                {
                    AbilityId = (int) abilityId
                };

                return _webSocket.SendProtoAsync(request, ExternalAccountAbilityResponse.Parser, ExternalRequestType.GetAccountAbilityGrade, token);
            }
        }

        public UniTask<Result<ExternalAccountAbilitiesResponse>> GetAllAbilitiesGrade(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ExternalAccountAbilitiesResponse>> Request(CancellationToken token)
            {
                UserRequest request = new();

                return _webSocket.SendProtoAsync(request, ExternalAccountAbilitiesResponse.Parser, ExternalRequestType.GetAllAccountAbilityGrades, token);
            }
        }

        public UniTask<Result> SetAbilityGrade(List<AbilityGradeData> abilitiesGradeData, CancellationToken token = default)
        {
            return _handler.Handle(Request, token).AsResult();

            UniTask<Result<EmptyMessage>> Request(CancellationToken token)
            {
                ExternalSaveAccountAbilityGradeRequest request = new();

                foreach (AbilityGradeData abilityGradeData in abilitiesGradeData)
                {
                    request.Ability.Add(
                        new AccountAbility
                        {
                            Id = (int) abilityGradeData.AbilityId,
                            Grade = abilityGradeData.Grade,
                        });
                }

                return _webSocket.SendProtoAsync(request, EmptyMessage.Parser, ExternalRequestType.SaveAccountAbilityGrade, token);
            }
        }

        public UniTask<Result<GetWinStreakResponse>> GetWinStreak(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<GetWinStreakResponse>> Request(CancellationToken token)
            {
                UserRequest userRequest = new();

                return _webSocket.SendProtoAsync(userRequest, GetWinStreakResponse.Parser, ExternalRequestType.GetWinStreak, token);
            }
        }

        public UniTask<Result<ExternalGetBloodRewardsResponse>> GetBloodRewards(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ExternalGetBloodRewardsResponse>> Request(CancellationToken token)
            {
                UserRequest userRequest = new();

                return _webSocket.SendProtoAsync(userRequest, ExternalGetBloodRewardsResponse.Parser, ExternalRequestType.GetBloodRewards, token);
            }
        }

        public UniTask<Result> SpendPocket(ItemQuantity item, CancellationToken cancellationToken = default)
        {
            return _handler.Handle(Request, cancellationToken).AsResult();

            UniTask<Result<EmptyMessage>> Request(CancellationToken token)
            {
                ExternalSpendPocketRequest externalSpendPocketRequest = new()
                {
                    Item = item
                };

                return _webSocket.SendProtoAsync(externalSpendPocketRequest, EmptyMessage.Parser, ExternalRequestType.SpendPocket, cancellationToken);
            }
        }

        public UniTask<Result> EquipPocket(ItemQuantity item, CancellationToken token = default)
        {
            return _handler.Handle(Request, token).AsResult();

            UniTask<Result<EmptyMessage>> Request(CancellationToken token)
            {
                ExternalEquipPocketRequest externalEquipPocketRequest = new()
                {
                    Item = item
                };

                return _webSocket.SendProtoAsync(externalEquipPocketRequest, EmptyMessage.Parser, ExternalRequestType.EquipPocket, token);
            }
        }

        public UniTask<Result<ExternalStatePocketResponse>> RequestPocketState(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ExternalStatePocketResponse>> Request(CancellationToken token)
            {
                EmptyMessage request = new();

                return _webSocket.SendProtoAsync(request, ExternalStatePocketResponse.Parser, ExternalRequestType.StatePocket, token);
            }
        }

        public UniTask<Result<ExternalGetLastUserFingerprintResponse>> GetFingerprint(CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ExternalGetLastUserFingerprintResponse>> Request(CancellationToken token)
            {
                UserRequest userRequest = new();

                return _webSocket.SendProtoAsync(userRequest, ExternalGetLastUserFingerprintResponse.Parser, ExternalRequestType.GetLastUserFingerprint, token);
            }
        }

        public UniTask<Result<ExternalOpenSackResponse>> OpenSack(int itemId, CancellationToken token = default)
        {
            return _handler.Handle(Request, token);

            UniTask<Result<ExternalOpenSackResponse>> Request(CancellationToken token)
            {
                ExternalOpenSackRequest openSackRequest = new()
                {
                    ItemId = itemId
                };

                return _webSocket.SendProtoAsync(openSackRequest, ExternalOpenSackResponse.Parser, ExternalRequestType.OpenSack, token);
            }
        }


        public UniTask<Result> SaveDailyQuests(UserDailyQuestInfo info, CancellationToken cancellationToken)
        {
            return _handler.Handle(Request, cancellationToken).AsResult();

            UniTask<Result<EmptyMessage>> Request(CancellationToken token)
            {
                ExternalSaveUserDailyQuestsRequest saveQuestsRequest = new()
                {
                    Data = info
                };

                return _webSocket.SendProtoAsync(saveQuestsRequest, EmptyMessage.Parser, ExternalRequestType.SaveUserDailyQuests, token);
            }
        }

        public UniTask<Result<ExternalGetUserDailyQuestsResponse>> LoadDailyQuests(CancellationToken cancellationToken)
        {
            return _handler.Handle(Request, cancellationToken);

            UniTask<Result<ExternalGetUserDailyQuestsResponse>> Request(CancellationToken token)
            {
                UserRequest request = new();

                return _webSocket.SendProtoAsync(request, ExternalGetUserDailyQuestsResponse.Parser, ExternalRequestType.GetUserDailyQuests, token);
            }
        }

        public UniTask<Result<ExternalAccountAbilityResponse>> UpgradeAbility(int ability, CancellationToken cancellationToken)
        {
            return _handler.Handle(Request, cancellationToken);

            UniTask<Result<ExternalAccountAbilityResponse>> Request(CancellationToken token)
            {
                ExternalAccountAbilityRequest request = new()
                {
                    AbilityId = ability
                };

                return _webSocket.SendProtoAsync(request, ExternalAccountAbilityResponse.Parser, ExternalRequestType.UpgradeAccountAbility, token);
            }
        }

        public UniTask<Result<global::Common.StatusResponse>> DowngradeAbility(int ability, CancellationToken cancellationToken)
        {
            return _handler.Handle(Request, cancellationToken);

            UniTask<Result<global::Common.StatusResponse>> Request(CancellationToken token)
            {
                ExternalAccountAbilityRequest request = new()
                {
                    AbilityId = ability
                };

                return _webSocket.SendProtoAsync(request, global::Common.StatusResponse.Parser, ExternalRequestType.DowngradeAccountAbility, token);
            }
        }

        public UniTask<Result<global::Common.StatusResponse>> SendPlayerHookStatistics(CancellationToken cancellationToken = default)
        {
            return _handler.Handle(Request, cancellationToken);

            UniTask<Result<global::Common.StatusResponse>> Request(CancellationToken token)
            {
                EmptyMessage request = new();

                return _webSocket.SendProtoAsync(request, global::Common.StatusResponse.Parser, ExternalRequestType.Harpooned, token);
            }
        }

        public UniTask<Result<ExternalAccountStatistic>> AccountStatistics(CancellationToken cancellationToken = default)
        {
            return _handler.Handle(Request, cancellationToken);

            UniTask<Result<ExternalAccountStatistic>> Request(CancellationToken token)
            {
                EmptyMessage request = new();

                return _webSocket.SendProtoAsync(request, ExternalAccountStatistic.Parser, ExternalRequestType.AccountStatistic, token);
            }
        }

        public UniTask<Result<ExternalGetGuideTimestampsResponse>> GetGuideTimestamps(CancellationToken cancellationToken)
        {
            return _handler.Handle(Request, cancellationToken);

            UniTask<Result<ExternalGetGuideTimestampsResponse>> Request(CancellationToken token)
            {
                UserRequest request = new();

                return _webSocket.SendProtoAsync(request, ExternalGetGuideTimestampsResponse.Parser, ExternalRequestType.GetGuideTimestamps, token);
            }
        }

        public UniTask<Result<ExternalGetUserSeasonResponse>> GetUserLeagueInfo(CancellationToken cancellationToken)
        {
            return _handler.Handle(Request, cancellationToken);

            UniTask<Result<ExternalGetUserSeasonResponse>> Request(CancellationToken token)
            {
                UserRequest request = new();

                return _webSocket.SendProtoAsync(request, ExternalGetUserSeasonResponse.Parser, ExternalRequestType.GetUserSeasonInfo, token);
            }
        }

        public UniTask<Result<ExternalGetLeagueInfoResponse>> GetLeagueInfo(uint league, CancellationToken cancellationToken)
        {
            return _handler.Handle(Request, cancellationToken);

            UniTask<Result<ExternalGetLeagueInfoResponse>> Request(CancellationToken token)
            {
                ExternalGetLeagueInfoRequest request = new()
                {
                    League = league
                };

                return _webSocket.SendProtoAsync(request, ExternalGetLeagueInfoResponse.Parser, ExternalRequestType.GetLeagueInfo, token);
            }
        }

        public UniTask<Result<ExternalGetSeasonInfoResponse>> GetSeasonInfo(uint season, CancellationToken cancellationToken)
        {
            return _handler.Handle(Request, cancellationToken);

            UniTask<Result<ExternalGetSeasonInfoResponse>> Request(CancellationToken token)
            {
                ExternalGetSeasonInfoRequest request = new()
                {
                    Season = season
                };

                return _webSocket.SendProtoAsync(request, ExternalGetSeasonInfoResponse.Parser, ExternalRequestType.GetSeasonInfo, token);
            }
        }

        public UniTask<Result<ExternalGetLeagueAwardResponse>> ClaimLeagueReward(CancellationToken cancellationToken)
        {
            return _handler.Handle(Request, cancellationToken);

            UniTask<Result<ExternalGetLeagueAwardResponse>> Request(CancellationToken token)
            {
                UserRequest request = new();

                return _webSocket.SendProtoAsync(request, ExternalGetLeagueAwardResponse.Parser, ExternalRequestType.GetLeagueAward, token);
            }
        }

        public UniTask<Result<ExternalGetClaimedKillLocalResponse>> GetClaimedMapKillRewards(int regionId, CancellationToken cancellationToken)
        {
            return _handler.Handle(Request, cancellationToken);

            UniTask<Result<ExternalGetClaimedKillLocalResponse>> Request(CancellationToken token)
            {
                ExternalGetClaimedKillLocalRewardsRequest request = new()
                {
                    Region = regionId
                };

                return _webSocket.SendProtoAsync(request, ExternalGetClaimedKillLocalResponse.Parser, ExternalRequestType.GetClaimedKillLocalRewards, token);
            }
        }

        public UniTask<Result<global::Common.StatusResponse>> ClaimMapKillRewards(int regionId, CancellationToken cancellationToken)
        {
            return _handler.Handle(Request, cancellationToken);

            UniTask<Result<global::Common.StatusResponse>> Request(CancellationToken token)
            {
                ExternalGetKillLocalRewardRequest request = new()
                {
                    Region = regionId
                };

                return _webSocket.SendProtoAsync(request, global::Common.StatusResponse.Parser, ExternalRequestType.GetKillLocalReward, token);
            }
        }

        public UniTask<Result<ExternalGetTelegramTokenResponse>> GetTelegramLink(CancellationToken cancellationToken)
        {
            return _handler.Handle(Request, cancellationToken);

            UniTask<Result<ExternalGetTelegramTokenResponse>> Request(CancellationToken token)
            {
                UserRequest request = new();

                return _webSocket.SendProtoAsync(request, ExternalGetTelegramTokenResponse.Parser, ExternalRequestType.GetTelegramToken, token);
            }
        }
        
        public UniTask<Result<ExternalDrinkPotionResponse>> DrinkPotion(int itemId, CancellationToken cancellationToken)
        {
            return _handler.Handle(Request, cancellationToken);

            UniTask<Result<ExternalDrinkPotionResponse>> Request(CancellationToken token)
            {
                ExternalDrinkPotionRequest request = new()
                {
                    ItemId = itemId
                };

                return _webSocket.SendProtoAsync(request, ExternalDrinkPotionResponse.Parser, ExternalRequestType.DrinkPotion, token);
            }
        }
    }
}