using System.Collections.Generic;
using Client.Common.Analytics.Infrastructure.Events;
using Client.Common.Analytics.Trackers.Abstract;
using Client.Common.Configs;
using Client.Utils.ECS.LocalWorld;
using Leopotam.Ecs;

namespace Client.Common.NpcTab.Analytics
{
    public class BuyItemAnalyticsTracker<TScope> : IAnalyticTracker where TScope : struct
    {
        private const string _EVENT_ID_ = "buy_item";
        
        private readonly LocalEcsWorld _world;

        private readonly EcsFilter<TScope> _scopeFilter;
        private readonly EcsFilter<BuyItemEvent>.Exclude<AnalyticsEventApplied<BuyItemEvent>> _buyEventsFilter;

        public BuyItemAnalyticsTracker(LocalEcsWorld world)
        {
            _world = world;

            _scopeFilter = world.GetFilter<EcsFilter<TScope>>();
            _buyEventsFilter = world.GetFilter<EcsFilter<BuyItemEvent>.Exclude<AnalyticsEventApplied<BuyItemEvent>>>();
        }

        public void Track()
        {
            if (_scopeFilter.IsEmpty())
            {
                return;
            }

            if (_buyEventsFilter.IsEmpty())
            {
                return;
            }

            foreach (int idx in _buyEventsFilter)
            {
                BuyItemEvent buyItemEvent = _buyEventsFilter.Get1(idx);
                FireEvent(buyItemEvent);
                _buyEventsFilter.GetEntity(idx).Get<AnalyticsEventApplied<BuyItemEvent>>();
            }
        }
        
        private void FireEvent(BuyItemEvent buyItemEvent)
        {
            EcsEntity analyticsEvent = _world.NewEntity();
            analyticsEvent.Get<AnalyticsCustomEvent>().Id = _EVENT_ID_;
            analyticsEvent.Get<AnalyticsParameters>().Parameters = new Dictionary<string, string>()
            {
                ["ItemID"] = buyItemEvent.ItemId.ToString(),
                ["Count"] = buyItemEvent.Count.ToString(),
                ["Balances"] = buyItemEvent.Balance.ToString(),
                ["Source"] = typeof(TScope).Name,
                ["Type"] = buyItemEvent.ItemType.ToString()
            };
        }
    }
}