using System;
using System.Collections.Generic;
using Client.Common.Items;
using Client.Common.Network.MetaNet;
using Client.Common.Storages;
using Client.Utils.GameLogger;
using Common;
using UnityEngine;
using ItemQuantity = Client.Common.Network.MetaNet.ItemQuantity;
using CurrencyType = Common.CurrencyType;
using ItemType = Common.ItemType;

namespace Client.Common.Player.Controllers
{
    public struct ItemChangedData
    {
        public int ItemId;
        public ItemType ItemType;
        public int Balance;
        public int Diff;
    }

    public sealed class PlayerInventory
    {
        public static class Items
        {
            public const int DEFAULT_HOOK = 72000;
            public const int FIRST_CONSUMABLE_HOOK = 72001;
            public const int DEFAULT_BOMB = 71000;
            public const int FIRST_CONSUMABLE_BOMB = 71001;

            public const ushort BLOOD_BOTTLE = 24003;
            public const ushort SOFT_CURRENCY = 60001;
            public const ushort HARD_CURRENCY = 60002;
            public const ushort BLOOD_CURRENCY = 60005;
            public const ushort RP_CURRENCY = 60006;
            public const ushort LEAGUE_TOKENS = 60007;

            public const int FIRST_CRAFTED_SHURIKEN = 11002;
            public const int FIRST_AMULET_ID = 17001;
            public const int ABSENT_AMULET = 17011;
            public const int BOTTLE_FOR_HEAL = 16001;
            public const int BOTTLE_FOR_HARPOON_EVADE = 16006;
            public const int BOTTLE_GINSENG = 16002;
            public const int FIRST_SHURIKEN_ID = 11001;
            public const int DEFAULT_SHURIKEN_SKIN_ID = 12000;
            public const int FIRST_SHURIKEN_SKIN_ID = 12001;
            public const int DEFAULT_OUTFIT_ID = 10001;
            public const int FIRST_OUTFIT_ID = 10002;
            public const int OUTFIT_FOR_HARPOON_EVADE = 10004;
            public const int POWER_STONE_ABSENT = 70000;
            public const int DRAGON_LOCKPICK_ID = 15003;
            public const int FIRST_SHURIKEN_PAINT_CHEST_ID = 14001;
            public const int DAILY_QUEST_SKIP = 50101;
            public const int FREE_ITEM_SOFT = 50102;
            public const int FREE_ITEM_HARD = 50103;
            public const int REPAIR_ITEM_HARD = 50104;
            public const int FIRST_POWER_STONE = 70001;
            public const int BLOOD_COLBA_ADS = 80001;
        }

        ClientInventoryResponse _data;
        readonly Dictionary<int, int> _itemsById;
        readonly Dictionary<int, List<int>> _itemsByType;
        readonly Dictionary<int, (float, float)> _uniquesById;

        public Wallet Rp { get; private set; } = new Wallet();
        public Wallet Soft { get; private set; } = new Wallet();
        public Wallet Hard { get; private set; } = new Wallet();
        public Wallet LeagueTokens { get; private set; } = new Wallet();
        public Wallet BloodBalance { get; private set; } = new Wallet();

        public event Action<ItemChangedData> ItemChanged;
        public event Action InventoryUpdated;

        private readonly Dictionary<CurrencyType, Wallet> _walletsMap;

        public PlayerInventory()
        {
            _data = ClientInventoryResponse.New();
            _itemsById = new Dictionary<int, int>(1024);

            _itemsByType = new Dictionary<int, List<int>>
            {
                {(int) ItemType.Outfit, new List<int>(64)},
                {(int) ItemType.OutfitCloth, new List<int>(64)},
                {(int) ItemType.Shuriken, new List<int>(64)},
                {(int) ItemType.ShurikenSkin, new List<int>(64)},
                {(int) ItemType.ShurikenSkinPaint, new List<int>(64)},
                {(int) ItemType.Amulet, new List<int>(64)},
                {(int) ItemType.Bottle, new List<int>(64)},
                {(int) ItemType.Herb, new List<int>(64)},
                {(int) ItemType.Lockpick, new List<int>(64)},
                {(int) ItemType.Sack, new List<int>(64)},
                {(int) ItemType.OutfitClothChest, new List<int>(64)},
                {(int) ItemType.ShurikenSkinPaintChest, new List<int>(64)},
                {(int) ItemType.Special, new List<int>(64)},
                {(int) ItemType.Treasure, new List<int>(64)},
                {(int) ItemType.BattleCondition, new List<int>(64)},
                {(int) ItemType.Currency, new List<int>(64)},
                {(int) ItemType.PowerStone, new List<int>(64)},
                {(int) ItemType.Hook, new List<int>(64)},
                {(int) ItemType.Bomb, new List<int>(64)},
            };
            _uniquesById = new Dictionary<int, (float, float)>(1024);

            _walletsMap = new Dictionary<CurrencyType, Wallet>
            {
                {CurrencyType.Soft, Soft},
                {CurrencyType.Hard, Hard},
                {CurrencyType.Rp, Rp},
                {CurrencyType.LeagueTokens, LeagueTokens},
                {CurrencyType.Blood, BloodBalance}
            };
        }

        public void Reset()
        {
            _data.Recycle();
            _itemsById.Clear();

            foreach (var pair in _itemsByType)
            {
                pair.Value.Clear();
            }

            _uniquesById.Clear();
        }

        public void SetItems(in ClientInventoryResponse response, ItemData itemData)
        {
            Reset();
            _data = response;
            _data.Items.Sort((a, b) => a.Id - b.Id);

            for (int i = 0, iMax = _data.Items.Count; i < iMax; i++)
            {
                ItemQuantity item = _data.Items[i];
                _itemsById[item.Id] = i;

                byte type = itemData.GetItemDataById(item.Id).Type;

                if (_itemsByType.TryGetValue(type, out List<int> typedList))
                {
                    typedList.Add(i);
                }
                else
                {
                    GameLogger.LogWarning($"[PlayerInventory] No storage found for type {(ItemType) type}");
                }
            }

            _data.Uniques.Sort((a, b) => a.Id - b.Id);

            for (int i = 0, iMax = _data.Uniques.Count; i < iMax; i++)
            {
                var unique = _data.Uniques[i];
                _uniquesById[unique.Id] = (unique.Value1, unique.Value2);
            }

            Rp.Set(GetItemCount(Items.RP_CURRENCY));
            Soft.Set(GetItemCount(Items.SOFT_CURRENCY));
            Hard.Set(GetItemCount(Items.HARD_CURRENCY));
            LeagueTokens.Set(GetItemCount(Items.LEAGUE_TOKENS));
            BloodBalance.Set(GetItemCount(Items.BLOOD_CURRENCY));
        }

        public void UpdateItemCount(
            int id,
            uint count,
            ItemType type)
        {
            uint prevCount = 0;
            ItemQuantity itemQuantity = new(id, count);

            if (!_itemsById.TryGetValue(id, out int idx))
            {
                idx = _data.Items.Count;
                _itemsById.Add(id, idx);
                _data.Items.Add(itemQuantity);
            }
            else
            {
                prevCount = _data.Items[idx].Count;
                _data.Items[idx] = itemQuantity;
            }

            if (_itemsByType.TryGetValue((byte)type, out List<int> typedList))
            {
                if (!typedList.Contains(idx))
                {
                    typedList.Add(idx);
                }
            }
            else
            {
                GameLogger.LogWarning($"[PlayerInventory] No storage found for type {type}");
            }

            switch (id)
            {
                case Items.RP_CURRENCY:
                    Rp.Set((int) count);

                    break;

                case Items.SOFT_CURRENCY:
                    Soft.Set((int) count);

                    break;

                case Items.HARD_CURRENCY:
                    Hard.Set((int) count);

                    break;

                case Items.LEAGUE_TOKENS:
                    LeagueTokens.Set((int) count);

                    break;

                case Items.BLOOD_CURRENCY:
                    BloodBalance.Set((int) count);

                    break;
            }

            ItemChangedData itemChangedData = new()
            {
                ItemId = id,
                ItemType = type,
                Balance = (int) count,
                Diff = (int) count - (int) prevCount
            };

            ItemChanged?.Invoke(itemChangedData);
        }

        public void FinishUpdate()
        {
            InventoryUpdated?.Invoke();
        }

        /// <summary>
        /// Gets items common data.
        /// </summary>
        /// <param name="idx">Index in inventory.</param>
        public ItemQuantity GetData(int idx)
        {
            return _data.Items[idx];
        }

        public int GetItemCount(int itemId)
        {
            return _itemsById.TryGetValue(itemId, out var idx) ? (int) _data.Items[idx].Count : 0;
        }

        public Wallet GetWallet(CurrencyType currencyType)
        {
            return _walletsMap[currencyType];
        }

        public bool Contains(int itemId)
        {
            return GetItemCount(itemId) > 0;
        }

        public bool Contains(int itemId, int count)
        {
            return GetItemCount(itemId) >= count;
        }

        public bool Contains(ItemComponent item)
        {
            return GetItemCount((int) item.Id) >= item.Count;
        }

        /// <summary>
        /// Gets items unique data.
        /// </summary>
        /// <param name="itemId">Item Id (not index in inventory).</param>
        /// <returns>Item1 - ability progress, Item2 - special.</returns>
        public (float, float) GetUniqueData(int itemId)
        {
            return _uniquesById.TryGetValue(itemId, out var val) ? val : default;
        }

        public List<int> GetItemsByType(ItemType itemType)
        {
            return _itemsByType[(int) itemType];
        }

        public bool IsEnough(CurrencyType currencyType, int count)
        {
            return currencyType switch
            {
                CurrencyType.Soft => Soft.IsEnough(count),
                CurrencyType.Hard => Hard.IsEnough(count),
                CurrencyType.Rp   => Rp.IsEnough(count),
                _                 => throw new Exception($"[CurrencyWallet] Cant process currency with type: {currencyType}")
            };
        }

        public bool IsEnough(CurrencyType currencyType, uint count)
        {
            return IsEnough(currencyType, (int) count);
        }
    }
}