using System;
using Client.Common.Network.MetaNet;
using Client.Utils.Extensions;
using Common;
using TimerType = Common.TimerType;

namespace Client.Common.Player.Timers
{
    [Serializable]
    public class PlayerTimer
    {
        public PlayerTimer(ClientTimerItem timerItem)
        {
            ItemId = timerItem.ItemId;
            TimerType = (TimerType)timerItem.TimerType;
            StartTime = ConvertToSeconds(timerItem.StartTime);
            EndTime = ConvertToSeconds(timerItem.DeadTime);
            Duration = EndTime - StartTime;
            
            long ConvertToSeconds(string milliseconds)
            {
                double realSeconds = double.Parse(milliseconds) / 1000;
                return (long)Math.Round(realSeconds);
            }
        }
        
        public PlayerTimer(Timer timer)
        {
            ItemId = (int)timer.ItemId;
            TimerId = timer.Id;
            TimerType = timer.Type;
            StartTime = ConvertToSeconds(timer.StartTime);
            EndTime = ConvertToSeconds(timer.DeadTime);
            Duration = EndTime - StartTime;
            
            long ConvertToSeconds(string milliseconds)
            {
                double realSeconds = double.Parse(milliseconds) / 1000;
                return (long)Math.Round(realSeconds);
            }
        }
        
        public PlayerTimer() { }
        public int ItemId { get; set; }
        public string TimerId { get; set; }
        public TimerType TimerType { get; set; }
        public long StartTime { get; set; }
        public long EndTime { get; set; }
        public long Duration { get; set; }

        public float GetProgressRatio()
        {
            return (float)this.GetRemainingUtcTimeInSeconds() / Duration;
        }

        public override bool Equals(object obj)
        {
            if (obj == null)
            {
                return false;
            }

            var other = (PlayerTimer)obj;
            return ItemId == other.ItemId && TimerType == other.TimerType;
        }

        public override int GetHashCode()
        {
            return ItemId * (int)TimerType;
        }

        public override string ToString()
        {
            return $"{TimerType} timer {ItemId}, start {DateTimeX.DateTimeFromSeconds(StartTime)}, end {DateTimeX.DateTimeFromSeconds(EndTime)}";
        }
    }
}