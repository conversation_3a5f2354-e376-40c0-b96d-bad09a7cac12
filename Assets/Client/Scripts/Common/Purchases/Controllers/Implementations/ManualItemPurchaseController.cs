using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Client.Common.Configs.Components;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Purchases.Data;
using Client.Utils.ResultTool.Results;
using Common;
using Cysharp.Threading.Tasks;

namespace Client.Common.Purchases.Controllers
{
    public class ManualItemPurchaseController
    {
        private readonly PlayerInventory _inventory;
        private readonly MetaNet _metaNet;

        public ManualItemPurchaseController(PlayerInventory inventory, MetaNet metaNet)
        {
            _inventory = inventory;
            _metaNet = metaNet;
        }

        public PurchaseResult HasSufficientResources(PriceData price)
        {
            return _inventory.IsEnough(price.CurrencyType, price.Price)
                ? PurchaseResult.Succeed
                : PurchaseResult.InsufficientFunds;
        }

        public PurchaseResult HasSufficientResources(params ItemComponent[] prices)
        {
            return prices.All(_inventory.Contains)
                ? PurchaseResult.Succeed
                : PurchaseResult.InsufficientFunds;
        }

        public UniTask<PurchaseResult> TryBuy(ItemComponent item, PriceData price, CancellationToken token = default)
        {
            PriceData totalPrice = price * (int)item.Count;
            
            return TryPurchaseWithValidation(
                validationFunc: () => UniTask.FromResult(HasSufficientResources(totalPrice)),
                spendFunc: () => _metaNet.SpendCurrency(totalPrice.CurrencyType, (int)totalPrice.Price, token),
                addItemFunc: () => _metaNet.AddItemToInventory(item, token)
            );
        }

        public UniTask<PurchaseResult> TryBuy(ItemComponent item, ItemComponent[] prices, CancellationToken token = default)
        {
            ItemComponent[] totalPrice = prices
                 .Select(price => price * (int)item.Count)
                 .ToArray();
            
            return TryPurchaseWithValidation(
                validationFunc: () => UniTask.FromResult(HasSufficientResources(totalPrice)),
                spendFunc: () => RemoveItemsFromInventory(totalPrice, token),
                addItemFunc: () => _metaNet.AddItemToInventory(item, token)
            );
        }

        private async UniTask<PurchaseResult> TryPurchaseWithValidation(
            Func<UniTask<PurchaseResult>> validationFunc,
            Func<UniTask<Result>> spendFunc,
            Func<UniTask<Result>> addItemFunc)
        {
            PurchaseResult isValid = await validationFunc();
            if (isValid != PurchaseResult.Succeed)
            {
                return isValid;
            }

            Result spendResult = await spendFunc();
            if (spendResult.IsFailure)
            {
                return PurchaseResult.Failed;
            }

            Result addItemResult = await addItemFunc();
            if (addItemResult.IsFailure)
            {
                return PurchaseResult.Failed;
            }

            return PurchaseResult.Succeed;
        }

        private async UniTask<Result> RemoveItemsFromInventory(IEnumerable<ItemComponent> prices, CancellationToken token)
        {
            Result removeResults = await _metaNet.RemoveItemsFromInventory(prices, token);

            return removeResults.IsSuccess
                ? Result.Success()
                : Result.Failure("[ManualItemPurchaseController] Failed to remove items from inventory");
        }
    }
}