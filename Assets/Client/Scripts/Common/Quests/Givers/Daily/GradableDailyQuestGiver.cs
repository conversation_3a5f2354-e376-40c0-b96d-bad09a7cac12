using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Client.Common.CSV;
using Client.Common.Ftue.Infrastructure;
using Client.Common.Network.MetaNet;
using Client.Common.Quests.Progress;
using Client.Common.Quests.Progress.Proxy;
using Client.Common.Quests.Systems.Configs;
using Client.Common.TimeGiver.Abstractions;
using Client.Utils.GameLogger;
using Client.Utils.ResultTool.Results;
using Common;
using Cysharp.Threading.Tasks;

namespace Client.Common.Quests.Givers.Daily
{
    public class GradableDailyQuestGiver
    {
        private readonly int[] _questCountByGrade;

        private readonly QuestDefs _defs;
        private readonly QuestUserProgress _progress;
        private readonly ITimeGiver _timeService;
        private readonly FtueProgress _ftue;
        private readonly RemoteDailyQuestStorage _storage;
        private readonly QuestProcessor _questProcessor;

        private int[][] _allQuestsByGrade;

        private int _currentGrade = 1;
        private int[][] _relevantQuestsByGrade;
        private Dictionary<int, List<int>> _rerolledQuests;
        
        public int MaxGrade => _allQuestsByGrade.Length;
        public IEnumerable<int> GetAvailableGrades()
        {
            return Enumerable.Range(1, MaxGrade);
        }

        public int CurrentGrade => _currentGrade;

        public GradableDailyQuestGiver(
            QuestDefs defs, 
            QuestUserProgress progress, 
            ITimeGiver timeService, 
            FtueProgress ftue, 
            GoogleDocsData googleDocsData, 
            MetaNet metaNet)
        {
            _ftue = ftue;
            _defs = defs;
            _progress = progress;
            _timeService = timeService;
            _questProcessor = new QuestProcessor(defs, progress);
            _storage = new RemoteDailyQuestStorage(metaNet);
            
            _questCountByGrade = new[]
            {
                googleDocsData.GetIntNumber("daily-quest.count.grade1"),
                googleDocsData.GetIntNumber($"daily-quest.count.grade2"), 
                googleDocsData.GetIntNumber($"daily-quest.count.grade3")
            };
        }

        public async UniTask Init(CancellationToken cancellationToken)
        {
            _allQuestsByGrade = FilterByRank(_defs.Quests.Values.Where(x => x.Type == QuestType.Daily).Select(x => (int)x.Id));

            Result<DailyQuests> result = await _storage.Load(cancellationToken);
            if (result.IsFailure || result.Value.CreatedTime < _timeService.UtcNow.Date)
            {
                await RefreshQuests(cancellationToken);
            }
            else
            {
                ImportDailyQuestData(result.Value);
            }
        }

        public UniTask RefreshQuests(CancellationToken cancellationToken = default)
        {
            _relevantQuestsByGrade = _questProcessor.RefreshQuests(_allQuestsByGrade, _questCountByGrade);
            _rerolledQuests = new Dictionary<int, List<int>>();
            TryAddTutorialQuest();
            DailyQuests data = new(_timeService.UtcNow.Date, _relevantQuestsByGrade.SelectMany(x => x).ToArray());
            return _storage.Save(data, cancellationToken);
        }

        public IEnumerable<ushort> GetRelevant()
        {
            return GetRelevant(_currentGrade).Select(x => (ushort)x);
        }
        
        public IEnumerable<ushort> GetAllQuests()
        {
            if (_ftue.IsCompleted(AccountStep.DailyQuestsUnlocked))
            {
                return _allQuestsByGrade.SelectMany(x => x).Select(x => (ushort)x);
            }
            else
            {
                return new[]{QuestIdents.TUTORIAL_QUEST};
            }
        }

        public IEnumerable<int> GetRelevant(int grade)
        {
            if (grade > MaxGrade)
            {
                return Array.Empty<int>();
            }

            return _relevantQuestsByGrade[grade - 1];
        }

        public UniTask SetNextGrade(CancellationToken cancellationToken = default)
        {
            _currentGrade++;
            GameLogger.Log("[Daily quests] next grade unlocked: "+_currentGrade);
            return _storage.Save(FlatData(), cancellationToken);
        }

        public async UniTask<int> Reroll(int questId, CancellationToken cancellationToken = default)
        {
            if (!_rerolledQuests.TryGetValue(CurrentGrade, out List<int> rerolledData))
            {
                rerolledData = new List<int>();
                _rerolledQuests.Add(CurrentGrade, rerolledData);
            }

            int listIndex = CurrentGrade - 1;
            int rerollQuest = _questProcessor.GetRandomRerollQuest(_allQuestsByGrade[listIndex], _relevantQuestsByGrade[listIndex], rerolledData, questId);

            GameLogger.Log("[Daily quests] rerolled "+questId+" to "+rerollQuest);
            
            int index = Array.IndexOf(_relevantQuestsByGrade[listIndex], questId);
            if (index < 0)
            {
                throw new Exception($"[Daily quests] Failed to reroll quest {questId}: not fount in current grade {_currentGrade}");
            }

            AddRerolledQuestAsFirst();

            await _storage.Save(FlatData(), cancellationToken);
            return rerollQuest;

            void AddRerolledQuestAsFirst()
            {
                List<int> questList = _relevantQuestsByGrade[listIndex].ToList();
                questList.RemoveAt(index);
                questList.Insert(0, rerollQuest);
                _relevantQuestsByGrade[listIndex] = questList.ToArray();
            }
        }

        public bool IsMasterRewardAvailable()
        {
            if (CurrentGrade == 0)
            {
                return false;
            }
            
            return QuestsClaimedCount(CurrentGrade) >= QuestTotalCountForGrade(CurrentGrade) && CurrentGrade <= MaxGrade;
        }

        private int QuestTotalCountForGrade(int grade)
        {
            return GetRelevant(grade).Count();
        }
        
        private int QuestsClaimedCount(int grade)
        {
            return GetRelevant(grade).Count(questId => IsRewardClaimed(questId));

            bool IsRewardClaimed(int questId)
            {
                return _progress.GetQuest((ushort)questId).GetRewardState() == QuestRewardState.Claimed;
            }
        }

        public bool IsAnyQuestCompleteNotClaimed()
        {
            return GetRelevant(_currentGrade).Any(questId => IsRewardAvailable(questId) && IsQuestCompleted(questId));
        }

        public IEnumerable<ushort> GetCompletedQuests()
        {
            return GetRelevant().Where(x => IsRewardAvailable(x) && IsQuestCompleted(x));
        }

        public DateTime GetNextUpdateTime()
        {
            return _timeService.UtcNow.AddDays(1).Date;
        }

        private bool IsRewardAvailable(int questId)
        {
            return _progress.GetQuest((ushort)questId).GetRewardState() == QuestRewardState.Available;
        }

        private bool IsQuestCompleted(int questId)
        {
            Quest questDef = _defs.Quests[(ushort)questId];
            IUserQuest questState = _progress.GetQuest((ushort)questId);
            return questDef.Goals.All(goal => questState.GetGoal(goal.Id).IsCompleted());
        }

        private void ImportDailyQuestData(DailyQuests data)
        {
            _currentGrade = data.CurrentGrade;
            _rerolledQuests = FilterByRankDict(data.Rerolled);
            _relevantQuestsByGrade = FilterByRank(data.Given);
        }

        private DailyQuests FlatData()
        {
            DailyQuests dailyQuests = new(_timeService.UtcNow.Date, 
                                          _relevantQuestsByGrade
                                              .SelectMany(x => x).ToArray())
            {
                Rerolled = _rerolledQuests.Values.SelectMany(x => x).ToArray(),
                
                CurrentGrade = _currentGrade
            };
            return dailyQuests;
        }

        private int[][] FilterByRank(IEnumerable<int> quests)
        {
            return FilterByRankDict(quests).Values.Select(x => x.ToArray()).ToArray();
        }

        private Dictionary<int, List<int>> FilterByRankDict(IEnumerable<int> quests)
        {
            var allQuestsByGrade = new Dictionary<int, List<int>>();

            foreach (int questId in quests)
            {
                Quest quest = _defs.Quests[(ushort)questId];
                if (!allQuestsByGrade.TryGetValue(quest.Rank, out List<int> rankedQuests))
                {
                    rankedQuests = new List<int>();
                    allQuestsByGrade.Add(quest.Rank, rankedQuests);
                }
                
                rankedQuests.Add(quest.Id);
            }

            return allQuestsByGrade;
        }

        private void TryAddTutorialQuest()
        {
            if (_ftue.IsCompleted(AccountStep.DailyQuestsUnlocked))
            {
                return;
            }
            
            _relevantQuestsByGrade[0][0] = QuestIdents.TUTORIAL_QUEST;
        }

        public void ResetQuests()
        {
            _questProcessor.ClearProgress(_allQuestsByGrade);
        }
    }
}