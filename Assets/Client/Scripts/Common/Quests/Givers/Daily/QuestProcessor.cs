using System;
using System.Collections.Generic;
using System.Linq;
using Client.Common.Network.MetaNet;
using Client.Common.Quests.Progress;
using Client.Common.Quests.Progress.Proxy;
using Client.Common.Quests.Systems.Configs;
using Client.Utils.Extensions;
using UnityEngine;

namespace Client.Common.Quests.Givers.Daily
{
    public class QuestProcessor
    {
        private readonly QuestDefs _defs;
        private readonly QuestUserProgress _progress;

        public QuestProcessor(QuestDefs defs, QuestUserProgress progress)
        {
            _defs = defs;
            _progress = progress;
        }

        public int[][] RefreshQuests(int[][] allQuests, int[] count)
        {
            int maxAvailableGrade = allQuests.Length;

            if (count.Length < maxAvailableGrade)
            {
                throw new Exception("[Daily quests] Invalid quest count by grade array");
            }

            int[][] newQuests = new int[maxAvailableGrade][];

            for (int grade = 0; grade < maxAvailableGrade; grade++)
            {
                newQuests[grade] = GetRandomDifferentQuests(allQuests[grade], count[grade]);
            }

            return newQuests;
        }

        public int GetRandomRerollQuest(
            int[] quests,
            int[] currentQuests,
            List<int> rerolled,
            int rerollFrom)
        {
            if (quests.Length - currentQuests.Length - rerolled.Count > 0)
            {
                int randomRerollQuest = quests
                                        .Except(currentQuests).Except(rerolled)
                                        .Shuffle().First();
                rerolled.Add(rerollFrom);

                return randomRerollQuest;
            }
            else
            {
                int orderedRerollQuest = rerolled.First();

                rerolled.RemoveAt(0);
                rerolled.Add(rerollFrom);

                return orderedRerollQuest;
            }
        }

        private int[] GetRandomDifferentQuests(int[] quests, int count)
        {
            List<int> relevantQuests = quests.OrderBy(_ => Guid.NewGuid()).ToList();
            List<string> types = new List<string>(count);
            List<int> validQuests = new List<int>(count);

            int index = 0;
            while (validQuests.Count < count && index < relevantQuests.Count)
            {
                int nextQuest = relevantQuests[index];
                TryAddQuestOfUniqueType(nextQuest);
                index++;
            }

            return validQuests.ToArray();

            void TryAddQuestOfUniqueType(int newQuest)
            {
                string type = _defs.Quests[(ushort) newQuest].Goals[0].Type;

                if (types.Contains(type))
                {
                    return;
                }

                types.Add(type);
                validQuests.Add(newQuest);
            }
        }

        public void ClearProgress(int[][] allQuests)
        {
            foreach (int[] gradeQuests in allQuests)
            {
                foreach (int id in gradeQuests)
                {
                    ClearQuestProgress(id);
                }
            }
        }

        private void ClearQuestProgress(int questInt)
        {
            ushort questId = (ushort) questInt;
            _progress.GetQuest(questId).SetQuestState(QuestState.Canceled);
            _progress.GetQuest(questId).SetRewardState(QuestRewardState.Available);

            IUserQuest quest = _progress.GetQuest(questId);

            foreach (QuestGoal goal in _defs.Quests[questId].Goals)
            {
                quest.GetGoal(goal.Id).SetProgress(0);
            }
        }
    }
}