using Client.Common.Analytics.Infrastructure.Services;
using Client.Common.Analytics.Trackers.Abstract;
using Client.Common.SceneLoading.Analytics.Data;

namespace Client.Common.SceneLoading.Analytics
{
    public class GoogleLoginAnalyticsTracker : IAnalyticTracker
    {
        private readonly AnalyticsService _analyticsService;
        private LoadingAnalyticsEvent _loadingAnalyticsEvent;
        
        public GoogleLoginAnalyticsTracker(AnalyticsService analyticsService)
        {
            _analyticsService = analyticsService;
        }
        
        public void Track()
        {
            FireEvent();
        }

        private void FireEvent()
        {
            _analyticsService.LogEvent("google_old_login");
        }
    }
}