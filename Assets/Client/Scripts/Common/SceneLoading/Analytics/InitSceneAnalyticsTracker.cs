using System.Collections.Generic;
using Client.Common.Analytics.Infrastructure.Services;
using Client.Common.Analytics.Trackers.Abstract;
using Client.Common.SceneLoading.Analytics.Data;

namespace Client.Common.SceneLoading.Analytics
{
    public class InitSceneAnalyticsTracker : IAnalyticTracker
    {
        private readonly AnalyticsService _analyticsService;
        private LoadingAnalyticsEvent _loadingAnalyticsEvent;
        
        public InitSceneAnalyticsTracker(AnalyticsService analyticsService)
        {
            _analyticsService = analyticsService;
        }
        
        public void SetEvent(LoadingAnalyticsEvent loadingAnalyticsEvent)
        {
            _loadingAnalyticsEvent = loadingAnalyticsEvent;
        }

        public void Track()
        {
            FireEvent(_loadingAnalyticsEvent);
        }

        private void FireEvent(LoadingAnalyticsEvent loadingAnalyticsEvent)
        {
            Dictionary<string, string> parameters  = new()
            {
                ["EventType"] = loadingAnalyticsEvent.EventType.ToString(),
            };

            if (!string.IsNullOrEmpty(loadingAnalyticsEvent.SceneName))
            {
                parameters["SceneName"] = loadingAnalyticsEvent.SceneName;
            }

            _analyticsService.LogEventWithParameters("loading_event", parameters);
        }
    }
}