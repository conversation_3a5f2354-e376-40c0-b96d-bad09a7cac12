using System.Collections.Generic;
using System.Linq;
using System.Threading;
using AppodealAds.Unity.Api;
using AppodealAds.Unity.Common;
using Client.Common.Services.Ads.Infrastructure;
using Client.Utils.Extensions;
using Client.Utils.GameLogger;
using Cysharp.Threading.Tasks;
using Debug = UnityEngine.Debug;

namespace Client.Common.Services.Ads.AppodealAds
{
    public class AppodealAdWrapper : IAdsWrapper, IAppodealInitializationListener
    {
#if UNITY_IOS
        private const string _GAME_ID_ = "2f0f822f1a5d16c3640e5baab3e674cf34924fde06f59c3a";
#elif UNITY_ANDROID
        private const string _GAME_ID_ = "c0db3a3b4b05f4f669b17679213299b6f150b5769d4fd390";
#elif UNITY_EDITOR
            private const string _GAME_ID_ = "c0db3a3b4b05f4f669b17679213299b6f150b5769d4fd390"; //Only for testing the functionality in the Editor
#endif

#if PRODUCTION
        private const bool _TEST_MODE_ = false;
#else
        private const bool _TEST_MODE_ = true;
#endif

        private IAdsLoader _interstitialAdsLoader;
        private IAdsLoader _rewardedAdsLoader;
        private IAppodealAdOpener _interstitialAdOpener;
        private IAppodealAdOpener _rewardedAdOpener;

        private AdServiceCallbackProvider _adServiceCallbackProvider;

        private bool _isInitialized;
        private bool _onAdsClosed;

        private CancellationTokenSource _cts;

        public void Init(AdServiceCallbackProvider adServiceCallbackProvider)
        {
            _cts = _cts.CancelAndCreate();
            _adServiceCallbackProvider = adServiceCallbackProvider;

            _interstitialAdsLoader = new AppodealAdInterstitialLoader();
            _rewardedAdsLoader = new AppodealAdRewardedLoader();

            _interstitialAdOpener = new AppodealAdOpenerInterstitialAd();
            _rewardedAdOpener = new AppodealAdOpenerRewardedAd(_rewardedAdsLoader);

            Appodeal.setTesting(_TEST_MODE_);
            Appodeal.setLogLevel(_TEST_MODE_ ? Appodeal.LogLevel.Verbose : Appodeal.LogLevel.None);
            Appodeal.setAutoCache(Appodeal.INTERSTITIAL, false);
            Appodeal.setAutoCache(Appodeal.REWARDED_VIDEO, false);

            int adTypes = Appodeal.INTERSTITIAL | Appodeal.REWARDED_VIDEO;

            if (!Appodeal.isInitialized(adTypes))
            {
                Appodeal.initialize(_GAME_ID_, adTypes, this);
            }
            else
            {
                InitInternal();
            }

            _isInitialized = MockEditorAds();
        }

        private void InitInternal()
        {
            _isInitialized = true;
            _interstitialAdsLoader.LoadAd();
            _rewardedAdsLoader.LoadAd();

            _interstitialAdsLoader.OnReady += OnAdsIntReady;
            _rewardedAdsLoader.OnReady += OnAdsReady;
            _interstitialAdsLoader.OnAdsClose += OnAdsClosed;
            _rewardedAdsLoader.OnAdsClose += OnAdsClosed;
            _interstitialAdOpener.Init();
            _rewardedAdOpener.Init();
            _onAdsClosed = false;
        }

        public bool CanOpenInterstitialAd()
        {
            return _isInitialized && _interstitialAdsLoader.CanShowAd();
        }

        public bool CanOpenRewardedAd()
        {
            return _isInitialized && _rewardedAdsLoader.CanShowAd();
        }

        public async UniTask<AdsResult> PlayInterstitial()
        {
            if (!_isInitialized)
            {
                GameLogger.LogErrorException("Appodeal Mobile Ads PlayInterstitial Ads - Ads was not initialized!", "AppodealAdInitInterstitial");

                return AdsResult.NotInitialization;
            }
            
            if (MockEditorAds())
            {
                return AdsResult.Finished;
            }

            if (!CanOpenInterstitialAd())
            {
                _interstitialAdsLoader.LoadAd();

                return AdsResult.Failed;
            }

            _interstitialAdOpener.ShowAd();
            await UniTask.WaitUntil(() => _onAdsClosed, cancellationToken: _cts.Token);
            _onAdsClosed = false;

            return _interstitialAdOpener.AdsResult;
        }

        public async UniTask<AdsResult> PlayRewarded()
        {
            if (!_isInitialized)
            {
                GameLogger.LogErrorException("Appodeal Mobile Ads PlayRewarded Ads - Ads was not initialized!", "AppodealAdInitRewarded");

                return AdsResult.NotInitialization;
            }

            if (MockEditorAds())
            {
                return AdsResult.Finished;
            }

            if (!CanOpenRewardedAd())
            {
                _rewardedAdsLoader.LoadAd();

                return AdsResult.Failed;
            }

            _rewardedAdOpener.ShowAd();
            await UniTask.WaitUntil(() => _onAdsClosed, cancellationToken: _cts.Token);
            _onAdsClosed = false;

            return _rewardedAdOpener.AdsResult;
        }

        private void OnAdsReady()
        {
            if (CanOpenRewardedAd())
            {
                _adServiceCallbackProvider.OnReadyToShowRewarded();
            }
        }

        private void OnAdsIntReady()
        {
            if (CanOpenInterstitialAd())
            {
                _adServiceCallbackProvider.OnReadyToShowInterstitial();
            }
        }

        private void OnAdsClosed()
        {
            _onAdsClosed = true;
        }

        public void Dispose()
        {
            _cts.CancelAndDispose();
            _interstitialAdsLoader.OnReady -= OnAdsIntReady;
            _rewardedAdsLoader.OnReady -= OnAdsReady;
            _interstitialAdsLoader.OnAdsClose -= OnAdsClosed;
            _rewardedAdsLoader.OnAdsClose -= OnAdsClosed;
            _interstitialAdsLoader?.Dispose();
            _rewardedAdsLoader?.Dispose();
        }

        public void onInitializationFinished(List<string> errors)
        {
            if (errors != null && errors.Count > 0)
            {
                GameLogger.LogErrorException(
                    $"Appodeal Ads initialization failed. Errors - {errors.Aggregate("", (current, error) => current + (error + "\n"))}", "AppodealAdInit");
                _isInitialized = false;

                return;
            }

            InitInternal();
        }

        private bool MockEditorAds()
        {
#if UNITY_EDITOR
            return true;
#endif
            return false;
        }
    }
}