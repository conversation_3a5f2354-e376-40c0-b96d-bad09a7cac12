using System;
using System.Threading;
using Client.Common.Audio;
using Client.Common.CSV;
using Client.Common.Services.Ads.AppodealAds;
using Client.Common.Services.Ads.GoogleAds;
using Client.Common.Services.Ads.Infrastructure;
using Client.Common.Services.Gdpr;
using Client.Utils.Extensions;
using Common;
using Cysharp.Threading.Tasks;
using GoogleMobileAds.Api;
using GoogleMobileAds.Ump.Api;

namespace Client.Common.Services.Ads.Service
{
    public class AdsService : IAdServiceWrapper, IDisposable
    {
        private readonly SoundManager _soundManager;
        private readonly GdprService _gdprService;
        private readonly GoogleDocsData _googleDocsData;

        private IAdsWrapper _adsWrapper;

        public Action OnInterstitialLoaded;
        public Action OnRewardedLoaded;
        public Action OnAdsCanceled;

        private AdServiceCallbackProvider _adServiceCallbackProvider = new();
        private CancellationTokenSource _cancellationSource = new();

        public bool AdsProcess { get; private set; }

        private UniTask<AdsResult> _currentTask;

        public AdsService(
            GdprService gdprService,
            SoundManager soundManager,
            GoogleDocsData googleDocsData)
        {
            _gdprService = gdprService;
            _soundManager = soundManager;
            _googleDocsData = googleDocsData;
        }

        public async UniTask Init()
        {
            bool appodealAds = _googleDocsData.GetIntNumber("ads.appodeal.ads") == 1;
            _adsWrapper = appodealAds ? new AppodealAdWrapper() : new GoogleAdsWrapper();
            MobileAds.RaiseAdEventsOnUnityMainThread = true;
            await InitGdprConsent();
        }

        private async UniTask InitGdprConsent()
        {
            await _gdprService.TryUpdateInfo(GdprData.ConsentRequest);

            if (_gdprService.IsConsentRequired())
            {
                await _gdprService.TryRequestConsent(
                    GdprData.ConsentRequest,
                    consented: result => { CanRequestAds(result.IsConsented); });
            }
            else
            {
                if (_gdprService.IsConsentNotRequired() || _gdprService.IsConsentObtained())
                {
                    InitInternal();
                }
            }
        }

        public void CanRequestAds(bool consentResult)
        {
            if (ConsentInformation.CanRequestAds() && consentResult)
            {
                InitInternal();
            }
        }

        private void InitInternal()
        {
            _cancellationSource.CancelAndDispose();
            _cancellationSource = new CancellationTokenSource();
            _adServiceCallbackProvider = new AdServiceCallbackProvider();

            _adServiceCallbackProvider.OnInterstitialReady += () => OnInterstitialLoaded?.Invoke();
            _adServiceCallbackProvider.OnRewardedReady += () => OnRewardedLoaded?.Invoke();

            _adsWrapper.Init(_adServiceCallbackProvider);
            AdsProcess = false;
        }

        public bool CanOpenInterstitialAd()
        {
            return _adsWrapper.CanOpenInterstitialAd();
        }

        public bool CanOpenRewardedAd()
        {
#if UNITY_EDITOR
            return true;
#endif
            return _adsWrapper.CanOpenRewardedAd();
        }

        public void CancelAndReinitAds()
        {
            Dispose();
            _adServiceCallbackProvider.OnCancel();
            OnAdsCanceled?.Invoke();

            InitInternal();
        }

        public async UniTask<AdsResult> PlayInterstitial()
        {
            AdsProcess = true;
            _soundManager.ProcessMasterVolume(1, 0, 0.1f);
            _currentTask = _adsWrapper.PlayInterstitial();

            AdsResult task = await _currentTask;
            _soundManager.ProcessMasterVolume(0, 1, 0.1f);
            AdsProcess = false;

            return task;
        }

        public async UniTask<AdsResult> PlayRewarded()
        {
            AdsProcess = true;
            _soundManager.ProcessMasterVolume(1, 0, 0.1f);
            _currentTask = _adsWrapper.PlayRewarded();

            AdsResult task = await _currentTask;
            _soundManager.ProcessMasterVolume(0, 1, 0.1f);
            AdsProcess = false;

            return task;
        }

        public void Dispose()
        {
            _adsWrapper?.Dispose();
        }
    }
}