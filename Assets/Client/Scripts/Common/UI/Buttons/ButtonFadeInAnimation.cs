using Client.Utils.Abstract;
using Client.Utils.CustomTweens.Data;
using Client.Utils.CustomTweens.TweenDescriptors;
using Client.Utils.CustomTweens.VFXService;
using Client.Utils.CustomTweens.VFXService.TweenSequence;
using UnityEngine;

namespace Client.Common.UI.Buttons
{
    [RequireComponent(typeof(CanvasGroup))]
    public class ButtonFadeInAnimation: MonoBehaviour
    {
        [SerializeField] private float _fadeInDelay = 1f;
        [SerializeField] private float _fadeInTime = 0.5f;
        [SerializeField] private AnimationCurve _fadeInCurve;
        
        private TweenSequence _tween;

        public void OnEnable()
        {
            TryGetComponent(out IInteractable interactable);
            interactable?.SetInteractable(false);
            
            _tween = new TweenSequence(new TweenManager());
            var tweenConfig = new TweenConfig<float>(0, 1, _fadeInTime, _fadeInDelay, animationCurve: _fadeInCurve);
            
            _tween.Append(new CanvasGroupTween(GetComponent<CanvasGroup>(), tweenConfig))
                  .Append(() => interactable?.SetInteractable(true))
                  .Play();
        }

        private void OnDisable()
        {
            _tween?.Dispose();
        }
    }
}