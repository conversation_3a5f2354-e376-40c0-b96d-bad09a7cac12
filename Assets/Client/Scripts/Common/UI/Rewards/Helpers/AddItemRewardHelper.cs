using System;
using System.Collections.Generic;
using System.Threading;
using Client.Common.Network.MetaNet;
using Client.Common.UI.InputLockService;
using Client.Common.UI.Rewards.Core;
using Client.Utils.ResultTool.Results;
using Common;
using Cysharp.Threading.Tasks;

namespace Client.Common.UI.Rewards.Helpers
{
    public class AddItemRewardHelper: IRewardPopupFactory
    {
        private readonly MetaNet _metaNet;
        private readonly RewardPopupFactory _rewardPopupFactory;
        private readonly FullScreenLocker _screenLocker;

        public AddItemRewardHelper(MetaNet metaNet, RewardPopupFactory rewardPopupFactory, FullScreenLocker screenLocker)
        {
            _metaNet = metaNet;
            _rewardPopupFactory = rewardPopupFactory;
            _screenLocker = screenLocker;
        }

        public async UniTask<DefaultRewardPresenter> Open(ItemComponent reward, RewardScreenConfig config = default, CancellationToken cancellationToken = default)
        {
            UniTask<Result> request = _metaNet.AddItemToInventory(reward, cancellationToken);
            UniTask<DefaultRewardPresenter> display = _rewardPopupFactory.Open(reward, config, cancellationToken);
            _screenLocker.Lock(cancellationToken);
            (Result addItemResult, DefaultRewardPresenter rewardPresenter) = await UniTask.WhenAll(request, display);
            _screenLocker.Unlock(cancellationToken);
            return rewardPresenter;
        }

        public async UniTask<DefaultRewardPresenter> Open(ItemComponent[] rewards, RewardScreenConfig config = default, CancellationToken cancellationToken = default)
        {
            UniTask<DefaultRewardPresenter> display = _rewardPopupFactory.Open(rewards, config, cancellationToken);
            _screenLocker.Lock(cancellationToken);
            (Result addItemResults, DefaultRewardPresenter rewardPresenter) = await UniTask.WhenAll(AddMultipleRewards(rewards, cancellationToken), display);
            _screenLocker.Unlock(cancellationToken);
            return rewardPresenter;
        }

        public async UniTask<DefaultRewardPresenter> Prepare(ItemComponent reward, RewardScreenConfig config = default, CancellationToken cancellationToken = default)
        {
            UniTask<Result> request = _metaNet.AddItemToInventory(reward, cancellationToken);
            UniTask<DefaultRewardPresenter> display = _rewardPopupFactory.Prepare(reward, config, cancellationToken);
            _screenLocker.Lock(cancellationToken);
            (Result addItemResult, DefaultRewardPresenter rewardPresenter) = await UniTask.WhenAll(request, display);
            _screenLocker.Unlock(cancellationToken);
            return rewardPresenter;
        }

        public async UniTask<DefaultRewardPresenter> Prepare(ItemComponent[] rewards, RewardScreenConfig config = default, CancellationToken cancellationToken = default)
        {
            UniTask<DefaultRewardPresenter> display = _rewardPopupFactory.Prepare(rewards, config, cancellationToken);
            _screenLocker.Lock(cancellationToken);
            (Result addItemResults, DefaultRewardPresenter rewardPresenter) = await UniTask.WhenAll(AddMultipleRewards(rewards, cancellationToken), display);
            _screenLocker.Unlock(cancellationToken);
            return rewardPresenter;
        }

        private UniTask<Result> AddMultipleRewards(IEnumerable<ItemComponent> rewards, CancellationToken cancellationToken)
        {
            return _metaNet.AddItemsToInventory(rewards, cancellationToken);;
        }
        
        public async UniTask<DefaultRewardPresenter> OpenWithPostReward(ItemComponent reward, RewardScreenConfig config = default, CancellationToken cancellationToken = default, Action onDone = null)
        {
            UniTask<DefaultRewardPresenter> display = _rewardPopupFactory.Open(reward, config, cancellationToken);
            _screenLocker.Lock(cancellationToken);
            DefaultRewardPresenter rewardPresenter = await display;
            rewardPresenter.Closed += () => OnViewClosed(reward, cancellationToken, onDone);
            _screenLocker.Unlock(cancellationToken);
            return rewardPresenter;
        }
        
        private async void OnViewClosed(ItemComponent reward, CancellationToken cancellationToken, Action onDone = null)
        {
            Result result = await _metaNet.AddItemToInventory(reward, cancellationToken);

            if (!result.IsSuccess)
            {
                return;
            }
            
            onDone?.Invoke();
        }
    }
}