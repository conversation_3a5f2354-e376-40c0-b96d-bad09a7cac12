using Client.Common.UI.Buttons;
using UnityEngine;

namespace Client.Common.UI.Rewards.Core
{
    public sealed class ButtonedRewardPopupView : RewardPopupViewBase
    {
        [SerializeField] private ActionButton _button;

        public override void OnOpen()
        {
            base.OnOpen();
            _button.Clicked += OnButtonClicked;
        }

        public override void OnPreClose()
        {
            base.OnPreClose();
            _button.Clicked -= OnButtonClicked;
        }

        private void OnButtonClicked()
        {
            _button.SetInteractable(false);
            SoundHelper?.PlayClickSound();
            SendInteractedEvent();
        }
    }
}