using System.Collections.Generic;
using Client.Utils.Extensions;
using UnityEngine;

namespace Client.Common.UI.ScrollController.Controllers
{
    public class CycleScrollController : BaseScrollController
    {
        private int _leftEdgeRawIndex;
        private int _rightEdgeRawIndex;
        private List<RectTransform> _defaultViewsOrder;

        public int ViewCount => Views.Count;

        public override int CurrentIndex => (int) Mathf.Repeat(CurrentRawIndex, Views.Count);

        public CycleScrollController(RectTransform container, IScrollPlayer scrollPlayer) :
            base(container, scrollPlayer)
        {
        }

        public override void Init()
        {
            _defaultViewsOrder = new List<RectTransform>(Views);
            base.Init();
        }

        public void Reset()
        {
            _leftEdgeRawIndex = 0;
            _rightEdgeRawIndex = ViewCount;

            Views.Clear();
            Views.AddRange(_defaultViewsOrder);

            RealignViews();
        }

        private void MoveViewItemLeftToRight()
        {
            RectTransform leftView = Views[0];
            Vector3 newViewPosition = GetViewPositionFor(_rightEdgeRawIndex, leftView);

            Views.RightShift(ViewCount - 1);
            leftView.localPosition = newViewPosition;
            _rightEdgeRawIndex++;
            _leftEdgeRawIndex++;
        }

        private void MoveViewItemRightToLeft()
        {
            RectTransform rightView = Views[^1];
            Vector3 newViewPosition = GetViewPositionFor(_leftEdgeRawIndex - 1, rightView);

            Views.LeftShift(ViewCount - 1);
            rightView.localPosition = newViewPosition;
            _rightEdgeRawIndex--;
            _leftEdgeRawIndex--;
        }

        protected override bool ValidateScrollIndex(int index)
        {
            if (index == _rightEdgeRawIndex)
            {
                MoveViewItemLeftToRight();
            }
            else if (index == _leftEdgeRawIndex - 1)
            {
                MoveViewItemRightToLeft();
            }

            return true;
        }

        protected override bool ValidateSetForceIndex(int index)
        {
            Reset();

            return base.ValidateSetForceIndex(index);
        }
    }
}