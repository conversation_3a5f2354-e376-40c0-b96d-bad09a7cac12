using System.Collections;
using Client.Common.Analytics.Helpers;
using Client.Common.Analytics.Trackers.Ftue;
using Client.Common.BlurTool.ScreenshotBlur;
using Client.Common.CodeFlow;
using Client.Common.Dialogs;
using Client.Common.Ftue.Infrastructure;
using Client.Common.Ftue.Infrastructure.Data;
using Client.Common.HintSystem;
using Client.Common.NpcTab.Controllers.ScrollIndexProviders;
using Client.Common.NpcTab.Data.TabTypes;
using Client.Common.NpcTab.Systems.Tabs;
using Client.Common.NpcTab.Views;
using Client.Common.ScreenTransitions.Components;
using Client.Common.UI.BackButton;
using Client.Common.UI.Buttons.Abstract;
using Client.Common.UI.Buttons.Pay;
using Client.Common.UI.Focuser;
using Common;
using Leopotam.Ecs;
using Leopotam.Localization;
using UnityEngine;

namespace Client.Alchemist.Systems.Ftue
{
    internal sealed class FtueFirstTraumaSystem : FtueSystemBase
    {
        private const string _HEAL_TRAUMA_KEY_ = "alchemist.heal.trauma.fully.subtext";
        
        private readonly DialogFtueHelper _dialogHelper = default;
        private readonly TabSwitcher _tabSwitcher = default;
        private readonly CsvLocalization _localization = default;
        private readonly ScreenshotBlurService _blurService = default;
        private readonly HintAnchors _hintAnchors = default;
        private readonly IAnalyticsHelper _analyticsHelper = default;

        private readonly ThePayButton _drinkButton;
        private readonly SpeedUpButtonView _adsButton;
        private ScrollIndexProviderHelper _scrollIndexProviderHelper;
        private EcsEntity _backButtonBlock;
        private bool _backButtonClicked;
        private UiFocuser _uiFocuser;

        public FtueFirstTraumaSystem(IButton closeButton, ThePayButton drinkButton, SpeedUpButtonView adsButton)
        {
            _drinkButton = drinkButton;
            _adsButton = adsButton;
            closeButton.Clicked += () => _backButtonClicked = true;
        }

        protected override AccountStep FtueStep => AccountStep.AlchemistTraumaTutorialFinished;
        protected override string LogDescription => "alchemist: trauma healing";
        protected override bool TutorialStartCondition()
        {
            return Ftue.Conditions.PlayerHasTrauma();
        }

        protected override void OnInit(CodeFlow<FtueProgress> flow)
        {
            _world.NewEntity().Get<SkipFadeOut>();
            _uiFocuser = new UiFocuser(_blurService);
            flow.Entry
                .Then(_analyticsHelper.FireContextualAnalyticsRoutine(FtueSteps.TraumaHealCallAccepted))
                .Then(_dialogHelper.OpenAndWaitDialogComplete(DialogIdents.TRAUMA_HEAL_1))
                .Then(HideBackButton())
                .Then(OpenTraumasTab())
                .Then(InitButtons())
                .Then(FocusDrinkButton(true))
                .Then(WaitForDrinkClick())
                .Then(_analyticsHelper.FireContextualAnalyticsRoutine(FtueSteps.TraumaHealClick))
                .Then(_commonHelper.CompleteStageRoutine(FtueProgressKeys.AlchemistDrinkHealBottle))
                .Then(_commonHelper.WaitTime(2f))
                .Then(FocusDrinkButton(false))
                .Then(_dialogHelper.OpenAndWaitDialogComplete(DialogIdents.TRAUMA_HEAL_2))
                .Then(EnableBackButton())
                .Then(WaitUserExit())
                .Then(_analyticsHelper.FireContextualAnalyticsRoutine(FtueSteps.TraumaHealComplete))
                ;
        }

        private IEnumerator InitButtons()
        {
            _adsButton.Hide();
            _drinkButton.SetSubText(_localization.Get(_HEAL_TRAUMA_KEY_));
            yield break;
        }

        private IEnumerator FocusDrinkButton(bool on)
        {
            if (on)
            {
                _uiFocuser.Focus(_drinkButton.GetComponent<RectTransform>(), _hintAnchors.HintRoot, UiFocuser.BackgroundType.Transparent);
            }
            else
            {
                _uiFocuser.Unfocus();
            }
            yield break;
        }

        private IEnumerator OpenTraumasTab()
        {
            _tabSwitcher.OpenTab((int) AlchemistTabType.Traumas);

            yield return null;
        }

        private IEnumerator WaitForDrinkClick()
        {
            bool drinkClicked = false;
            
            _drinkButton.ShowGreenGlow(true);
            _drinkButton.Clicked += () => drinkClicked = true;

            while (!drinkClicked)
            {
                yield return null;
            }

            _drinkButton.ShowGreenGlow(false);
        }
        
        private IEnumerator HideBackButton()
        {
            _backButtonBlock = _world.NewEntity();
            _backButtonBlock.Get<BackButtonHide>();
            yield break;
        }

        private IEnumerator EnableBackButton()
        {
            _backButtonBlock.Del<BackButtonHide>();
            yield break;
        }

        private IEnumerator WaitUserExit()
        {
            while (!_backButtonClicked)
            {
                yield return null;
            }
        }
    }
}