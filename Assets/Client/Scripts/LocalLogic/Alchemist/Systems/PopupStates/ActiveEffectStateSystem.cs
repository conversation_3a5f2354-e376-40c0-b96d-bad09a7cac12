using System;
using System.Collections.Generic;
using Client.Alchemist.Controllers;
using Client.Common.Analytics.Helpers;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Items;
using Client.Common.CSV;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Timer;
using Client.Common.NpcTab.Views;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.Common.Services.Ads;
using Client.Common.Services.Ads.Analytics;
using Client.Common.Services.Ads.Infrastructure;
using Client.Common.Traumas.Views;
using Client.Common.UI.Buttons.Pay;
using Client.Common.UI.InputLockService;
using Client.Common.UI.ScrollController.Components;
using Client.Utils.ECS.ECSStateMachine.Systems;
using Client.Utils.Extensions.EcsExtensions;
using Client.Utils.FireBase;
using Client.Utils.GameLogger;
using Client.Utils.ResourceLoading;
using Client.Utils.ResultTool.Results;
using Common;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using Leopotam.Localization;
using TimerType = Client.Common.Network.MetaNet.TimerType;

namespace Client.Alchemist.Systems.PopupStates
{
    internal abstract class ActiveEffectStateSystem<TEffectMarker, TStateMachine, TTabMarker> : StateSystem<TStateMachine>
        where TEffectMarker : struct
        where TStateMachine : IEcsFeature
        where TTabMarker : struct, IScrollableMarker
    {
        protected readonly MetaNet MetaNet = default;
        protected readonly PlayerManager PlayerManager = default;
        protected readonly GoogleDocsData GoogleDocsData = default;
        protected readonly CsvLocalization Localization = default;
        protected readonly ResourceLoadingService ResourceLoadingService = default;
        protected readonly FullScreenLocker FullScreenLocker = default;
        protected readonly IAnalyticsHelper AnalyticsHelper = default;

        protected readonly EcsFilter<ViewComponent<EffectTimerView>, ItemIdData, TimerTypeData, TEffectMarker, CurrentItem> CurrentEffect;
        protected readonly EcsFilter<TimerEndEvent> TimerEnds;

        protected abstract ThePayButton CompleteButton { get; }
        protected abstract SpeedUpButtonView SpeedUpButton { get; }
        
        protected EcsEntity CurrentConfig;
        
        protected override void OnEnable()
        {
            base.OnEnable();

            foreach (int index in CurrentEffect)
            {
                CurrentConfig = CurrentEffect.GetEntity(index);
                EffectTimerView effectView = CurrentEffect.Get1(index).View;

                if (effectView == null)
                {
                    try
                    {
                        throw new CrashlyticsException("ActiveEffectStateSystem OnEnable effectView is null");
                    }
                    catch (Exception e)
                    {
                        GameLogger.LogErrorException(e.Message, "ActiveEffectStateSystemOnEnableEffectView");
                        continue;
                    }
                }
                
                effectView.Show();

                OnEnableInternal();
                CompleteButton.Clicked += ExtendByBottle;
                SpeedUpButton.Clicked += SendAdsAnalyticsClicked;
                SpeedUpButton.AdsStarted += SendAdsAnalyticsStarted;
                SpeedUpButton.AdsFinished += ExtendByPromo;
                SpeedUpButton.AdsFinished += SendAdsAnalyticsFinished;
                CompleteButton.Show();
                SpeedUpButton.Show();
            }
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            
            CompleteButton.Clicked -= ExtendByBottle;
            SpeedUpButton.Clicked -= SendAdsAnalyticsClicked;
            SpeedUpButton.AdsStarted -= SendAdsAnalyticsStarted;
            SpeedUpButton.AdsFinished -= ExtendByPromo;
            SpeedUpButton.AdsFinished -= SendAdsAnalyticsFinished;
            CompleteButton.Hide();
            SpeedUpButton.Hide();

            if (CurrentConfig.IsAlive())
            {
                EffectTimerView effectView = CurrentConfig.Get<ViewComponent<EffectTimerView>>().View;
                effectView.Hide();
            }
        }

        protected virtual void OnEnableInternal()
        {
            InitExtendButtonsVisual().Forget();
        }
        protected abstract UniTaskVoid InitExtendButtonsVisual();

        protected bool TryExtend(int itemId)
        {
            int itemCount = PlayerManager.Session.Inventory.GetItemCount(itemId);
            bool isEnough = itemCount > 0;

            if (!isEnough)
            {
                CompleteButton.SetState(ThePayButtonState.Locked);
                CompleteButton.SetAttractionActive(true);
            }

            return isEnough;
        }

        private void ExtendByPromo(AdsResult showResult)
        {
            if (showResult != AdsResult.Finished)
            {
                GameLogger.LogWarning($"[Alchemist] Effect extend by promo is not finished. Effect: {CurrentEffect.Get2(0).Id}.");
                return;
            }

            PerformExtendByPromo().Forget();
        }
        
        private void ExtendByBottle()
        {
            PerformExtendByBottle().Forget();
        }

        protected override void OnUpdate()
        {
            base.OnUpdate();
            foreach (int timerIndex in TimerEnds)
            {
                ref TimerEndEvent timerEndEvent = ref TimerEnds.Get1(timerIndex);
                if (timerEndEvent.ItemId != CurrentEffect.Get2(0).Id)
                {
                    continue;
                }
                OnTimerEnd().Forget();
            }
        }

        protected virtual async UniTask OnTimerEnd()
        {
            await UniTask.NextFrame();
            EffectRequestHelper.CreateRemoveEffectRequest<TTabMarker>(World, CurrentEffect.Get2(0).Id);
        }
        
        private void SendAdsAnalyticsClicked()
        {
            SendAdsAnalyticsRequest(AdsActions.Click);
        }
        
        private void SendAdsAnalyticsStarted()
        {
            SendAdsAnalyticsRequest(AdsActions.Start);
        }
        
        private void SendAdsAnalyticsFinished(AdsResult showResult)
        {
            SendAdsAnalyticsRequest(showResult);
        }
        
        private void SendAdsAnalyticsRequest(AdsActions action)
        {
            SendAdsAnalyticsRequest(AdsResult.Skipped, action: action);
        }

        private void SendAdsAnalyticsRequest(AdsResult showResult, AdsActions action = AdsActions.None)
        {
            int id = CurrentEffect.Get2(0).Id;
            World.NewEntity().Get<AdsAnalyticsRequest>().Construct(id.ToString(), GetPromoOperationType(), showResult, action, AnalyticsHelper.GetAllRewardsAnalyticsRow(new List<ItemComponent>
            {
                new()
                {
                    Id = id,
                    Count = 1,
                }
            }));
        }

        protected abstract UniTaskVoid PerformExtendByPromo();

        protected abstract UniTaskVoid PerformExtendByBottle();
        protected abstract string GetPromoOperationType();

        protected async UniTask ChangeEffectTime(PlayerTimer timer, int extendValue)
        {
            Result<ChangeTimerResponse> changeTimer = await MetaNet.ChangeTimer((TimerType)timer.TimerType, timer.ItemId, extendValue);
            if (changeTimer.IsFailure)
            {
                GameLogger.LogError($"[{nameof(ActiveEffectStateSystem<TEffectMarker, TStateMachine, TTabMarker>)}] Extend timer error {changeTimer.Error}");
            }

            ItemComponent item = new()
            {
                Id = AdsIdents.PROMO_ITEM_ID,
                Count = 1,
            };
            Result removeItem = await MetaNet.RemoveItemFromInventory(item);
            if (removeItem.IsFailure)
            {
                GameLogger.LogWarning($"[{nameof(ActiveEffectStateSystem<TEffectMarker, TStateMachine, TTabMarker>)}] Promo spend error: {removeItem.Error}");
            }

            PlayerManager.Session.Timers.ChangeTimer(changeTimer.Value.Timer);
        }

        protected bool TryGetCurrentTimer(out PlayerTimer timer)
        {
            int itemId = CurrentEffect.Get2(0).Id;
            global::Common.TimerType timerType = CurrentEffect.Get3(0).Type;

            if (!PlayerManager.Session.Timers.TryGetTimer(itemId, timerType, out timer))
            {
                GameLogger.LogWarning($"[Alchemist] Can't find active timer {timerType} {itemId} for extension.");
                return false;
            }

            return true;
        }
    }
}