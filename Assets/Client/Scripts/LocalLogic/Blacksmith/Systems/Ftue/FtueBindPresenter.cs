using Client.Common.Network.Authentication.Credentials.Social.Abstract;
using Client.Utils.ViewService;
using Client.Common.Network.Authentication.Credentials.Social.Facebook;
using Client.Common.Network.Authentication.Credentials.Social.Google;
using Cysharp.Threading.Tasks;
using Client.Utils.StateSwitcher;
using Plugins.iOS.iOSAtt.Scripts;

namespace Client.Blacksmith.Systems.Ftue
{
    public class FtueBindPresenter : Presenter<FtueBindView, EmptyPresenterModel>
    {
        private readonly FacebookAuthService _facebook;
        private readonly GoogleAuthService _google;
        
        private CheckAtt _checkAtt;

        public FtueBindPresenter(
            FacebookAuthService facebook,
            GoogleAuthService google)
        {
            _facebook = facebook;
            _google = google;
        }

        protected override void OnPreOpen()
        {
            base.OnPreOpen();
            View.SetButtonsInteractable(TheActiveState.Active);
            View.SetContentBlock();

            View.CloseButton.Clicked += HideWithoutWait;

            View.Google.Clicked += LoginGoogle;
            View.Facebook.Clicked += LoginFacebook;
            _checkAtt = new CheckAtt(View.InfoContextScreenView);
        }

        protected override void OnPreClose()
        {
            base.OnPreClose();

            View.CloseButton.Clicked -= HideWithoutWait;

            View.Google.Clicked -= LoginGoogle;
            View.Facebook.Clicked -= LoginFacebook;
        }

        private void HideWithoutWait()
        {
            Hide().Forget();
        }

        private void LoginFacebook()
        {
            Login(_facebook);
        }

        private void LoginGoogle()
        {
            Login(_google);
        }

        private async void Login(BaseSocialAuthService service)
        {
            if (!_checkAtt.Check())
            {
                return;
            }
            
            View.SetButtonsInteractable(TheActiveState.Inactive);
            if (await service.TryLogin())
            {
                View.SetSuccessBlock();
            }
            else
            {
                View.SetButtonsInteractable(TheActiveState.Active);
            }
        }
    }
}