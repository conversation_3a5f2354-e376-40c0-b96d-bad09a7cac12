using Client.Common.UI.Buttons;
using Client.Common.UI.Buttons.Texted;
using Client.Utils.StateSwitcher;
using Client.Utils.ViewService;
using DG.Tweening;
using Plugins.iOS.iOSAtt.Scripts;
using UnityEngine;

namespace Client.Blacksmith.Systems.Ftue
{
    public class FtueBindView : View
    {
        [SerializeField] private TheTextedButton _signInGoogle;
        [SerializeField] private TheTextedButton _signInFacebook;
        [SerializeField] private InfoContextScreenView _infoContextScreenView;
        [SerializeField] private GameObject _contentBlock;
        [SerializeField] private GameObject _successBlock;

        [SerializeField] private ActionButton _close;
        
        public InfoContextScreenView InfoContextScreenView => _infoContextScreenView;

        public ActionButton CloseButton => _close;

        public TheTextedButton Google => _signInGoogle;
        public TheTextedButton Facebook => _signInFacebook;

        public void SetButtonsInteractable(TheActiveState state)
        {
            _signInGoogle.SetState(state);
            _signInFacebook.SetState(state);
        }

        public void SetContentBlock()
        {
            _contentBlock.SetActive(true);
            _successBlock.SetActive(false);
        }

        public void SetSuccessBlock()
        {
            float successFadeDuration = 0.5f;
            
            _contentBlock.SetActive(false);
            _successBlock.SetActive(true);
            _successBlock.GetComponent<CanvasGroup>().DOFade(1, successFadeDuration).From(0);
        }
    }
}