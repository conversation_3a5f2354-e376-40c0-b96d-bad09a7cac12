using System.Collections;
using Client.Blacksmith.Data;
using Client.Blacksmith.UnityComponents;
using Client.Common.Analytics.Helpers;
using Client.Common.Analytics.Trackers.Ftue;
using Client.Common.CodeFlow;
using Client.Common.Dialogs;
using Client.Common.Ftue.Infrastructure;
using Client.Common.Ftue.Infrastructure.Data;
using Client.Common.Network.Authentication.Credentials.Social;
using Client.Common.ScreenTransitions.Components;
using Client.Utils.ResourceLoading;
using Client.Utils.ViewService;
using Common;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;

namespace Client.Blacksmith.Systems.Ftue.Shuriken
{
    public class FtuePart1System : FtueSystemBase, IEcsDestroySystem
    {
        private readonly DialogFtueHelper _dialogHelper = default;
        private readonly BlacksmithTabView _tabView = default;
        private readonly IAnalyticsHelper _analyticsHelper = default;
        private readonly PopupViewService _viewService = default;
        private readonly SocialCredentialsProvider _credentialsProvider = default;

        private bool _tabClosed;
        private FtueBindPresenter _bindPresenter;

        protected override AccountStep FtueStep => AccountStep.FirstEnterBattleRegion;
        protected override string LogDescription => "blacksmith: introduction";

        protected override bool TutorialStartCondition() => !IsCompleted(FtueProgressKeys.BlacksmithUnlock);

        protected override void OnInit(CodeFlow<FtueProgress> flow)
        {
            _tabView.Closed += OnCloseClicked;
            _world.NewEntity().Get<SkipFadeOut>();
            
            PrepareBindPresenter().Forget();

            flow.Entry
                .Then(CloseButtonSetActive(false))
                .Then(_dialogHelper.OpenDialog(DialogIdents.BLACKSMITH_INTRO, transitionOnClose: false))
                .Then(_analyticsHelper.FireContextualAnalyticsRoutine(FtueSteps.OpenBlacksmithDialog))
                .Then(
                    _dialogHelper.TrackDialogs(
                        true,
                        (int) FtueSteps.DialogBlacksmith1_1,
                        (int) FtueSteps.DialogBlacksmith1_2))
                .Then(_analyticsHelper.FireContextualAnalyticsRoutine(FtueSteps.SkipBlueprintHint))
                .Then(_commonHelper.CompleteStageRoutine(FtueProgressKeys.BlacksmithUnlock))
                .Then(OpenBindScreen())
                .Then(CloseButtonSetActive(true))
                .Then(TrackCloseWindow())
                .Then(_analyticsHelper.FireContextualAnalyticsRoutine(FtueSteps.CloseBlacksmithWindow))
                ;
        }

        private async UniTask PrepareBindPresenter()
        {
            ResourceConfig resourceConfig = new()
            {
                LoadFrom = LoadFromType.Addressables,
                Path = LocalIdents.Paths.FTUE_BIND_VIEW_PATH
            };

            _bindPresenter = await _viewService.Prepare<FtueBindPresenter, FtueBindView, EmptyPresenterModel>(
                new EmptyPresenterModel(), opener: new InstantOpener<FtueBindView>(),
                resourceConfig: resourceConfig
            );
        }

        private IEnumerator OpenBindScreen()
        {
            if (!_credentialsProvider.HasCredentials)
            {
                _bindPresenter.Show();
            }

            yield break;
        }


        public void Destroy()
        {
            _tabView.Closed -= OnCloseClicked;
        }

        private IEnumerator CloseButtonSetActive(bool on)
        {
            _tabView.SetCloseButton(on);

            yield break;
        }

        private IEnumerator TrackCloseWindow()
        {
            while (!_tabClosed)
            {
                yield return null;
            }

            _tabView.Closed -= OnCloseClicked;
        }

        private void OnCloseClicked()
        {
            _tabClosed = true;
        }
    }
}