using System;
using System.Threading;
using Client.Common.Analytics.Infrastructure.Services;
using Client.Common.Audio;
using Client.Common.Audio.Banks;
using Client.Common.Configs;
using Client.Common.Configs.Core;
using Client.Common.Configs.Providers.Effects;
using Client.Common.Configs.Providers.Items.ItemEffects;
using Client.Common.ECS;
using Client.Common.Ftue.Infrastructure;
using Client.Common.GameSettings;
using Client.Common.Gdpr;
using Client.Common.LocalNotifications;
using Client.Common.Network.Authentication.Credentials.Local;
using Client.Common.Network.Connection;
using Client.Common.Network.Http;
using Client.Common.Network.Loaders;
using Client.Common.Player.Controllers;
using Client.Common.SceneLoading;
using Client.Common.SceneLoading.SceneProviders;
using Client.Common.ScreenTransitions;
using Client.Common.TelegramBot;
using Client.Connection.AppMaintenance.Views;
using Client.Connection.Controllers;
using Client.Connection.Systems;
using Client.Connection.Views;
using Client.Utils.Extensions;
using Client.Utils.GameLogger;
using Cysharp.Threading.Tasks;
using UnityEngine;
using VContainer;

namespace Client.Connection
{
    public sealed class ConnectionStartup : MonoBehaviour
    {
        [SerializeField] private NoConnectionView _noConnectionUI;
        [SerializeField] private MaintenanceView _maintenanceUi;
        [SerializeField] private MainLoader _loader;
        [SerializeField] private IntermediateAudio _audio;

        private CancellationTokenSource _cts;
        private bool _startDone;
        
        [Inject] private AnimatedSceneLoader _sceneLoader;
        [Inject] private PlayerManager _playerManager;
        [Inject] private GlobalWorld _globalWorld;
        [Inject] private ConfigService _configService;
        [Inject] private TransitionService _transitionService;
        [Inject] private SoundManager _soundManager;
        [Inject] private GameSettingsGiver _gameSettingsGiver;
        [Inject] private ConnectorTrackPlayerLogin _connectorTrackPlayerLogin;
        [Inject] private ConnectLoader _connectLoader;
        [Inject] private AnalyticsService _analyticsService;
        [Inject] private FtueProgress _ftueProgress;
        [Inject] private EffectsRawDataLoader _effectsRawDataLoader;
        [Inject] private ItemEffectsProviderLoader _itemEffectsProviderLoader;
        [Inject] private ConfigServiceLoader _configServiceLoader;
        [Inject] private NotificationsLoader _notificationsLoader;
        [Inject] private GdprLoader _gdprLoader;
        [Inject] private GlobalWorldLoader _globalWorldLoader;
        [Inject] private DataLoadersFactory _dataLoadersFactory;
        [Inject] private IAPLoader _iapLoader;
        [Inject] private NetworkController _networkController;
        
        private readonly TelegramCredentialsStorage _credentialsStorage = new();

        private async void Start()
        {
            _cts = _cts.CancelAndCreate();

            _loader.Construct(_playerManager);
            _globalWorld.Dispose();
            _configService.Dispose();

            // await metaNet.DisconnectAsync();
            
            _playerManager.Session.Reset();
            _transitionService.Dispose();
            
            await HttpNet.ApplyServerType(_cts.Token);

            Connector connector = new(_networkController);
            connector.ConnectionCompleted += () => _connectorTrackPlayerLogin.Track();

            connector.AddConnectLoader(_connectLoader)
                     .Add(_dataLoadersFactory.LoadAll())
                     .Add(_effectsRawDataLoader)
                     .Add(_itemEffectsProviderLoader)
                     .Add(_configServiceLoader)
                     .Add(_notificationsLoader)
                     .Add(_gdprLoader)
                     .Add(_globalWorldLoader)
                     .Add(_iapLoader)
                     ;

            try
            {
                await Connect(connector, _playerManager.Session, _ftueProgress, _transitionService, _analyticsService, _globalWorld, _cts.Token);
            }
            catch (OperationCanceledException e)
            {
                GameLogger.LogWarning("[ConnectionStartup] Connection was cancelled");
                return;
            }
            
            _gameSettingsGiver.CountryState = _playerManager.Session.AccountInfo.Country;
            
            _analyticsService.SetUserId(_playerManager.Session.AccountInfo.UserId, HasTelegramLink());

            _startDone = true;
        }

        private async UniTask Connect(
            Connector connector, 
            PlayerSession playerSession, 
            FtueProgress ftueProgress, 
            TransitionService transitionService, 
            AnalyticsService analyticsService, 
            GlobalWorld world,
            CancellationToken cancellationToken)
        {
            ConnectionView connectionView = new(_noConnectionUI, _maintenanceUi, _loader);
            ISceneProvider connectionNextSceneProvider = new ConnectionNextSceneProvider(playerSession, ftueProgress);
            ConnectionNextSceneLoader nextSceneLoader = new(_sceneLoader, connectionNextSceneProvider, world.World, playerSession);
            ConnectionAudioController connectionSound = new(_audio);
            ConnectionController connectionController = new(connector, nextSceneLoader, connectionView, connectionSound, playerSession);

            transitionService.SetAppOffCanvas(false);
            transitionService.SetAppOffScreen(false);
            
            await connectionController.Connect(cancellationToken);

            ConnectorTrackPlayerLogin connectorTrackPlayerLogin = new(ftueProgress, analyticsService);
            connectorTrackPlayerLogin.Track();
        }

        private void OnDestroy()
        {
            if (_startDone)
            {
                return;
            }
            
            _cts.CancelAndDispose();
        }

        private bool HasTelegramLink() => _credentialsStorage.TryLoad(out TelegramCredentials credentials) && credentials.TelegramId > 0;
    }
}