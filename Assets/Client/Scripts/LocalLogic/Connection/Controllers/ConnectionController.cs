using System.Threading;
using Client.Common.Audio;
using Client.Common.Player.Controllers;
using Client.Connection.Controllers;
using Client.Connection.Views;
using Cysharp.Threading.Tasks;
#if !UNITY_EDITOR
using Firebase.Crashlytics;
#endif

namespace Client.Connection.Systems
{
    internal class ConnectionController
    {
        private readonly Connector _connector;
        private readonly ConnectionView _connectionView;
        private readonly ConnectionAudioController _audio;
        private readonly PlayerSession _playerSession;
        private readonly ConnectionNextSceneLoader _nextSceneLoader;
        private readonly ConnectionTimeMonitor _connectionTimeMonitor;

        public ConnectionController(Connector connector, ConnectionNextSceneLoader nextSceneLoader, ConnectionView connectionView, ConnectionAudioController audio, PlayerSession playerSession)
        {
            _connector = connector;
            _connectionView = connectionView;
            _audio = audio;
            _playerSession = playerSession;
            _nextSceneLoader = nextSceneLoader;
            _connectionTimeMonitor = new ConnectionTimeMonitor();

            _connector.ConnectionStarted += OnConnectionStarted;
            _connector.Authenticated += OnAuthenticated;
            _connector.ConnectionProgressed += OnConnectionProgressed;
            _connector.ConnectionCompleted += OnConnectionCompleted;
        }

        public async UniTask Connect(CancellationToken cancellationToken)
        {
            _connectionTimeMonitor.StartTimer().Forget();
            _audio.Start();
            await _connector.Connect(cancellationToken);
        }

        private void OnConnectionStarted()
        {
            _connectionView.UpdateStateView(_connector.ConnectionState);
        }

        private void OnAuthenticated()
        {
#if !UNITY_EDITOR
            Crashlytics.SetUserId(_playerSession.AccountInfo.UserId);
#endif
        }

        private void OnConnectionProgressed(float progress)
        {
            _connectionView.SetProgress(progress);
        }

        private void OnConnectionCompleted()
        {
            _connectionTimeMonitor.StopTimer();
            _audio.Complete();
            _connectionView.SetProgress(1);
            _nextSceneLoader.LoadNextScene();
        }
    }
}