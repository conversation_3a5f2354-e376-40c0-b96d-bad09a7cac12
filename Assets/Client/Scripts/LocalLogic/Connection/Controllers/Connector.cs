using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Client.Common.AppMaintenance.Controllers;
using Client.Common.Network.Connection;
using Client.Common.Network.Http;
using Client.Common.Network.Loaders;
using Client.Connection.Data;
using Client.Utils.GameLogger;
using Client.Utils.Loaders;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace Client.Connection.Systems
{
    internal class Connector
    {
        private const int _RECONNECT_DELAY_IN_SECONDS_ = 2;

        public event Action Authenticated;
        public event Action ConnectionStarted;
        public event Action<float> ConnectionProgressed;
        public event Action ConnectionCompleted;

        public ConnectionState ConnectionState { get; private set; } = ConnectionState.None;

        private readonly List<BaseLoader> _loaders;
        private readonly MaintenanceHelper _maintenanceHelper;
        
        private readonly NetworkController _networkController;

        public Connector(NetworkController networkController)
        {
            _loaders = new List<BaseLoader>(10);
            _maintenanceHelper = new MaintenanceHelper();
            _networkController = networkController;
        }

        public Connector Add(BaseLoader loader)
        {
            _loaders.Add(loader);

            return this;
        }

        public Connector Add(IEnumerable<BaseLoader> loaders)
        {
            _loaders.AddRange(loaders);

            return this;
        }
        
        public Connector AddConnectLoader(ConnectLoader connectLoader)
        {
            connectLoader.Completed += CompleteAuthentication;
            
            _loaders.Add(connectLoader);
            
            return this;
        }

        public async UniTask Connect(CancellationToken cancellationToken)
        {
            await TrySetMaintenanceState(cancellationToken);
            
            if (await TryConnect(cancellationToken) == false)
            {
                return;
            }

            while (true)
            {
                StartConnection();

                //todo: Separate data load form connection process
                if (await ConnectAll(cancellationToken))
                {
                    break;
                }

                await UniTask.Yield(cancellationToken);
            }

            CompleteConnection();
        }

        private async UniTask<bool> TryConnect(CancellationToken cancellationToken)
        {
            if (ConnectionState == ConnectionState.Maintenance)
            {
                MaintenanceStart();
                return false;
            }

            if (ConnectionState == ConnectionState.NoConnection)
            {
                await Task.Delay(1000 * _RECONNECT_DELAY_IN_SECONDS_, cancellationToken);
            }
            
            SetConnectionState(ConnectionState.Connected);

            return true;
        }

        private async UniTask<bool> ConnectAll(CancellationToken cancellationToken)
        {
            for (int i = 0; i != _loaders.Count; ++i)
            {
                LoadResult result = await _loaders[i].Load(cancellationToken);
                
                if (result.IsSuccessful == false)
                {
                    SetConnectionState(ConnectionState.NoConnection);
                    GameLogger.LogWarning($"Connection error. Loader: {_loaders[i].GetType().Name}. Error: {result.Error}");

                    return false;
                }

                ProgressConnection((float)i / _loaders.Count);
            }

            TryReconnect();

            return true;
        }

        private void TryReconnect()
        {
            CheckConnectionState(ConnectionState, ConnectionState.Connected);
            SetConnectionState(ConnectionState.Connected);
        }

        private async UniTask TrySetMaintenanceState(CancellationToken cancellationToken)
        {
            (bool maintenance, string error) result = await _maintenanceHelper.MaintenanceHttp(HttpNet.WEBDAV_HOST, cancellationToken);
            if (result.maintenance)
            {
                SetConnectionState(ConnectionState.Maintenance);
            }
        }

        private void MaintenanceStart()
        {
            StartConnection();
        }

        private void StartConnection()
        {
            ConnectionStarted?.Invoke();
        }

        private void CompleteAuthentication()
        {
            Authenticated?.Invoke();
        }

        private void ProgressConnection(float progress)
        {
            ConnectionProgressed?.Invoke(progress);
        }

        private void CompleteConnection()
        {
            ConnectionCompleted?.Invoke();
        }

        private void SetConnectionState(ConnectionState state)
        {
            ConnectionState = state;
        }

        private void CheckConnectionState(ConnectionState prevState, ConnectionState nextState)
        {
            if (prevState == ConnectionState.NoConnection && (nextState == ConnectionState.None || nextState == ConnectionState.Connected))
            {
                _networkController.Reconnect();
            }
        }
        
    }
}