using Client.Common;
using Client.Common.Abilities.Helpers;
using Client.Common.Abilities.Infrastructure.Loaders;
using Client.Common.AI.Data;
using Client.Common.AI.Givers;
using Client.Common.Analytics.Helpers;
using Client.Common.Audio;
using Client.Common.CameraController;
using Client.Common.Ftue.Helpers;
using Client.Common.Loadout.Traumas;
using Client.Common.NpcTab.Controllers;
using Client.Common.Player.Controllers;
using Client.Common.ScreenTransitions.Views.VSScreen;
using Client.Common.Traumas;
using Client.Common.TriNavMeshes;
using Client.Common.UI.Labels;
using Client.Common.UI.Rewards.Core;
using Client.Common.VContainer;
using Client.FPV.Battle.Abilities.Factory;
using Client.FPV.Battle.BattleEditor;
using Client.FPV.Battle.BattleResultScroll.BattleStatsScreen;
using Client.FPV.Battle.BattleResultScroll.Loadout;
using Client.FPV.Battle.Camera.UnityComponents;
using Client.FPV.Battle.Effects;
using Client.FPV.Battle.HUD;
using Client.FPV.Battle.HUD.Arrows.Controllers;
using Client.FPV.Battle.HUD.Arrows.Data;
using Client.FPV.Battle.Systems.AI.Throw;
using Client.FPV.Battle.Systems.Ending.Factories;
using Client.FPV.Battle.Systems.Ftue;
using Client.FPV.Battle.Systems.Ftue.FtueHints;
using Client.FPV.Battle.UnityComponents;
using Client.LocalLogic.FPV.Common.Services;
using Client.Utils.Coroutines;
using Client.Utils.CustomTweens.VFXService;
using Client.Utils.CustomTweens.VFXService.Abstractions;
using Client.Utils.ViewService;
using Client.Utils.Visible;
using NaughtyAttributes;
using UnityEngine;
using VContainer;

namespace Client.FPV.Battle
{
    internal sealed class BattleLifeTimeScope : BaseLifeTimeScope
    {
        private const string _BATTLE_LOCALS_PATH_ = "Locations/{0:D2}/Battle/{1:D2}/{2}";
        private const string _BATTLE_LOCAL_VISUAL_ = "_Visual";

        [SerializeField] private BattleCommonVisual _commonVisual;
        
        [HorizontalLine()]
        [Header("Player hud")]
        [SerializeField] private TextView _centerHintText;
        
        [Header("Arrows")]
        [SerializeField] private Transform _arrowsHolder;
        [SerializeField] private GameObject _leftArrow;
        [SerializeField] private GameObject _rightArrow;
        
        [Header("Player anchors")]
        [SerializeField] private Transform _userHandAnchor;
        [SerializeField] private UnitCameraComponent _unitCamera;
        
        [HorizontalLine()]
        [Header("Fullscreen")]
        [SerializeField] private Transform _overlayRootTransform;
        [SerializeField] private Transform _overlaySafeZone;
        [SerializeField] private VSScreenView _vsScreenView;
        [SerializeField] private HUDView _hudView;

        protected override void Configure(IContainerBuilder builder)
        {
            base.Configure(builder);

            PlayerManager playerManager = Parent.Container.Resolve<PlayerManager>();

            builder.Register<UISoundHelper>(Lifetime.Scoped);
            builder.Register<TweenManager>(Lifetime.Scoped).As<ITweenManager>().AsSelf().WithParameter(false).WithParameter(_ => (ICoroutineRunner)null);
            builder.Register<VisibilityController>(Lifetime.Scoped).WithParameter<IVisible>(_hudView);
            builder.Register<AbilityGradeStorage>(Lifetime.Scoped);
            builder.Register<FtueHintFlowProvider>(Lifetime.Scoped).WithParameter((RectTransform)_overlayRootTransform).WithParameter(_overlaySafeZone).WithParameter(_centerHintText).WithParameter(_userHandAnchor);
            builder.Register<FtueHintHelper>(Lifetime.Scoped).WithParameter(_overlaySafeZone);
            builder.Register<BattleFtueHelper>(Lifetime.Scoped);
            builder.Register<CommonFtueHelper>(Lifetime.Scoped);
            builder.RegisterInstance(CreateArrowBundle());
            builder.Register<PlayerWinStreak>(Lifetime.Scoped);
            if (playerManager.Session.RegionId == 0)
            {
                builder.Register<BotDifficultyFtueProvider>(Lifetime.Scoped).As<IBotDifficultyProvider>().AsSelf();
            }
            else
            {
                builder.Register<BotDifficultyProvider>(Lifetime.Scoped).As<IBotDifficultyProvider>().AsSelf();
            }

            builder.Register<LocalAnalyticsHelper>(Lifetime.Scoped).As<IAnalyticsHelper>().AsSelf();
            builder.Register<AbilityFactoryHelper>(Lifetime.Scoped);
            builder.Register<BattleAbilityFactoryProvider>(Lifetime.Scoped);
            builder.Register<EffectsControllerFactory>(Lifetime.Scoped);
            builder.Register(resolver => resolver.Resolve<EffectsControllerFactory>().Create(), Lifetime.Scoped);
            builder.Register<BattleAiConditions>(Lifetime.Scoped);
            builder.Register<TriNavMesh>(Lifetime.Scoped);
            builder.RegisterInstance(Resources.Load<BattleLocalVisual>(string.Format(_BATTLE_LOCALS_PATH_, playerManager.Session.RegionId, playerManager.Session.BattleId, _BATTLE_LOCAL_VISUAL_)));
            builder.Register<EnvironmentObserver>(Lifetime.Scoped);
            builder.Register<CameraSupport>(Lifetime.Scoped).WithParameter("mainCamera", UnityEngine.Camera.main).WithParameter("uiCamera", GameObject.FindWithTag(Idents.Tags.UiCamera).GetComponent<UnityEngine.Camera>());
            builder.RegisterInstance(_commonVisual);
            builder.RegisterInstance(CreateCameraEffectsController());
            builder.RegisterInstance(_hudView);
            builder.RegisterInstance(_vsScreenView);

            builder.Register<PopupViewService>(Lifetime.Scoped).WithParameter(_overlayRootTransform);
            builder.Register<RewardPopupFactory>(Lifetime.Scoped);
            builder.Register<BotHistoryGenerator>(Lifetime.Scoped);

            builder.Register<HealTraumaHelper>(Lifetime.Scoped);
            builder.Register<LoadoutBattleResultController>(Lifetime.Scoped);
            builder.Register<LoadoutTraumasController>(Lifetime.Scoped).WithParameter((BankTransferController)null);
            builder.Register<BattleResultScrollFactory>(Lifetime.Scoped);
        }
        
        private ArrowBundle CreateArrowBundle()
        {
            BattleArrowController leftArrowController = new(_leftArrow);
            BattleArrowController rightArrowController = new(_rightArrow);

            return new ArrowBundle(leftArrowController, rightArrowController, _arrowsHolder);
        }
        
        private CameraEffectsController CreateCameraEffectsController()
        {
            CameraEffectsController result = new(_unitCamera.transform);
            
            result.Add(new MovementCameraModifier(_unitCamera.Data.Configuration))
                  .Configure(_unitCamera.Data.IdleHeadbobData);
            result.Add(new HeadshotCameraModifier());
            result.Add(new ShakeCameraModifier());

            return result;
        }
    }
}