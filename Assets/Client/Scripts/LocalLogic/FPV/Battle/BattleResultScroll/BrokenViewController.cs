using System;
using System.Threading;
using Client.Common.Durability;
using Client.Common.Items;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Purchases.Controllers;
using Client.Common.Purchases.Data;
using Client.Common.Services.Ads;
using Client.Common.Services.Ads.Infrastructure;
using Client.Common.UI.ScrollController;
using Client.TopDown.Region.Durability;
using Client.Utils.GameLogger;
using Client.Utils.ResultTool.Results;
using Common;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Object = UnityEngine.Object;
using WeaponSkin = External.WeaponSkin;

namespace Client.FPV.Battle.BattleResultScroll
{
    public class BrokenViewController : BattleResultViewControllerBase
    {
        private readonly ItemData _itemData;
        private readonly ItemType _brokenItemType;
        private DurabilityService _durabilityService;

        private BrokenView _brokenView;
        private float _adsRepairPercent;

        private readonly CancellationToken _token;
        private readonly ItemPurchaseController _purchaseController;

        public BrokenViewController(ItemData itemData, ItemType itemType, CancellationToken token, ItemPurchaseController purchaseController)
        {
            _itemData = itemData;
            _brokenItemType = itemType;
            AdsId = "repair_skin";
            _token = token;
            _purchaseController = purchaseController;
        }

        public override async UniTask<bool> TryCreate(IScrollController scrollController)
        {
            _adsRepairPercent = GoogleDocsData.GetNumber("repair.ads.percent");
            if (_brokenItemType != ItemType.Outfit && _brokenItemType != ItemType.ShurikenSkin)
            {
                GameLogger.LogError("Wrong broken item type");

                return false;
            }

            _durabilityService = new DurabilityService(PlayerManager, _itemData, GoogleDocsData);

            if (_brokenItemType == ItemType.Outfit && _durabilityService.IsBrokenOutfit(PlayerManager.Session.Progress.OutfitId))
            {
                await CreateView(scrollController, BattleResultIdents.Paths.OUTFIT_VIEW_PATH, PlayerManager.Session.Progress.OutfitId);

                return true;
            }

            if (_brokenItemType == ItemType.ShurikenSkin && _durabilityService.IsBrokenShurikenSkin(PlayerManager.Session.Progress.WeaponId))
            {
                await CreateView(scrollController, BattleResultIdents.Paths.SHURIKEN_SKIN_VIEW_PATH, PlayerManager.Session.Progress.WeaponId);
                _brokenView.SetShurikenSkin(PlayerManager.Session.Progress.WeaponSkinId);

                return true;
            }

            return false;
        }

        private async UniTask CreateView(IScrollController scrollController, string path, int itemId)
        {
            BrokenView asset = await ResourceLoadingService.FromResources(path).LoadAsync<BrokenView>();
            _brokenView = Object.Instantiate(asset, scrollController.Container);
            scrollController.Add(_brokenView.GetComponent<RectTransform>());

            GameObject iconAsset = await QuantifiedIconLoadingService.FromResources(itemId).LoadAsync<GameObject>();
            _brokenView.Init(iconAsset);
        }

        protected override void InitView()
        {
            View.TopPanel.gameObject.SetActive(true);
            View.TopPanel.Set(0, PlayerManager.Session.Inventory.Hard.Amount, 0);

            View.SetTitleActive(true);
            View.SetTitleText(Localization.Get(BattleResultIdents.Localization.BROKEN_TITLE), Localization.Get(BattleResultIdents.Localization.BROKEN_SUBTITLE));
            View.SetTitleColor(View.TitleColorRed);

            ExitButtonInit(View.ExitButton.gameObject, Localization.Get(BattleResultIdents.Localization.EXIT_BROKEN));
            
            string payButtonText = Localization.Get(BattleResultIdents.Localization.PAYED_REVIVE);
            string payButtonSubtext = Localization.Get(BattleResultIdents.Localization.PAY_BROKEN);
            int payHardPrice = (int)_itemData.GetItemPrice(PlayerInventory.Items.REPAIR_ITEM_HARD).Price;
            InitPayButton(payButtonText, payButtonSubtext, payHardPrice, PlayerManager.Session.Inventory.Hard.Amount);
            
            View.AdsButton.gameObject.SetActive(true);
            View.AdsButton.SetSubText(string.Format(Localization.Get(BattleResultIdents.Localization.ADS_BROKEN), _adsRepairPercent * 100));
            View.AdsButton.AdsFinished += OnAdsFinished;
            ActivateButtons();

            _brokenView.Play();
        }

        protected override void OnAdsFinished(AdsResult showResult)
        {
            SendAdsAnalyticsRequest(showResult, _brokenItemType.ToString());
            if (showResult == AdsResult.Finished)
            {
                AdsRepair(_token).Forget();
            }
            else
            {
                ActivateButtons();
            }
        }

        private async UniTaskVoid AdsRepair(CancellationToken token)
        {
            bool isDurabilityRestoreSuccessful = await CalculateRepairCountAndRepair(RepairType.Ads, token);

            if (isDurabilityRestoreSuccessful)
            {
                await SpendPromo(token);
                Next();
            }
            else
            {
                GameLogger.LogWarning($"[{nameof(BrokenViewController)}] Durability is not restored successfully.");
                ActivateButtons();
            }
        }

        private void Next()
        {
            _brokenView.Stop();
            View.ExitButton.gameObject.SetActive(false);
            View.PayButton.gameObject.SetActive(false);
            View.AdsButton.gameObject.SetActive(false);
            UserMadeChoice = true;
        }

        private UniTask<bool> CalculateRepairCountAndRepair(RepairType repairType, CancellationToken token)
        {
            RepairItemProvider repairItemProvider = new(PlayerManager, _itemData, _durabilityService);
            ClientItemData item = repairItemProvider.GetItem();
            int repairCount = 0;
            switch (repairType)
            {
                case RepairType.Full:
                    repairCount = (int) item.MaxDurability;
                    break;
                case RepairType.Ads:
                    repairCount = (int) (item.MaxDurability * _adsRepairPercent);
                    break;
            }

            return ItemRepair(repairCount, token);
        }

        protected override void OnPayClick()
        {
            PayRepairItem(_token).Forget();
        }

        private async UniTaskVoid PayRepairItem(CancellationToken token)
        {
            DeactivateButtons();

            try
            {
                PurchaseResult result = await _purchaseController.TryBuy(PlayerInventory.Items.REPAIR_ITEM_HARD, token);
                if (result != PurchaseResult.Succeed)
                {
                    return;
                }

                bool success = await CalculateRepairCountAndRepair(RepairType.Full, token);
                if (success)
                {
                    Next();
                }
                else
                {
                    ActivateButtons();
                }
            }
            catch (OperationCanceledException e)
            {
                ActivateButtons();
            }
        }

        private async UniTask<bool> ItemRepair(int repairCount, CancellationToken token)
        {
            bool isDurabilityRestoreSuccessful = false;
            if (_brokenItemType == ItemType.Outfit)
            {
                Result outfitDurability = await MetaNet.SetOutfitDurability(PlayerManager.Session.Progress.OutfitId, repairCount, token);
                isDurabilityRestoreSuccessful = outfitDurability.IsSuccess;
            }
            else if (_brokenItemType == ItemType.ShurikenSkin)
            {
                int skinId = PlayerManager.Session.Progress.WeaponStorage.GetSkinId(PlayerManager.Session.Progress.WeaponId);
                WeaponSkin weaponSkin = new()
                {
                    WeaponId = PlayerManager.Session.Progress.WeaponId,
                    SkinId = skinId,
                    Durability = repairCount
                };
                Result skinsDurability = await MetaNet.SetWeaponSkinsDurability(weaponSkin, token);
                isDurabilityRestoreSuccessful = skinsDurability.IsSuccess;
            }

            return isDurabilityRestoreSuccessful;
        }

        private async UniTask SpendPromo(CancellationToken token)
        {
            ItemComponent item = new()
            {
                Id = AdsIdents.PROMO_ITEM_ID,
                Count = 1
            };
            
            Result removeItem = await MetaNet.RemoveItemFromInventory(item, token);
            if (removeItem.IsFailure)
            {
                GameLogger.LogWarning($"[{nameof(BrokenViewController)}] Promo spend error: {removeItem.Error}");
            }
        }

        protected override void OnExitClick()
        {
            Next();
        }
    }
}