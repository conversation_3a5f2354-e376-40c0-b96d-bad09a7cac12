using Client.Common.Ftue.Infrastructure;
using Client.Common.Loadout.Traumas;
using Client.Common.UI.ScrollController;
using Cysharp.Threading.Tasks;

namespace Client.FPV.Battle.BattleResultScroll
{
    public class HealControllerAdapter : IBattleResultViewController
    {
        private readonly LoadoutTraumasController _loadoutTraumasController;
        private readonly FtueProgress _ftue;

        public HealControllerAdapter(LoadoutTraumasController loadoutTraumasController, FtueProgress ftue)
        {
            _loadoutTraumasController = loadoutTraumasController;
            _ftue = ftue;
        }
        public async UniTask<bool> TryCreate(IScrollController scrollController)
        {
            if (!_ftue.Conditions.IsTraumaCalculationEnabled() || !_loadoutTraumasController.CanHealAnyTrauma())
            {
                return false;
            }

            await _loadoutTraumasController.PrepareScreen();
            _loadoutTraumasController.SetTopButton(false);

            return true;
        }

        public UniTask Open(BattleResultContainerView view)
        {
            return _loadoutTraumasController.OpenAndWaitClose();
        }
    }
}