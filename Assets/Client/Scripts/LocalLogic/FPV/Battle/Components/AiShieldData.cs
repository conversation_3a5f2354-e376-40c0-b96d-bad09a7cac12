using System;
using Client.Common.AI.Data;
using Client.Common.Configs.Components.Ai;
using JetBrains.Annotations;
using NaughtyAttributes;
using UnityEngine;

namespace Client.FPV.Battle.Components
{
    public sealed class AiShieldData : MonoBehaviour
    {
        [SerializeField] [ReadOnly] [UsedImplicitly]
        private int Rank;

        [ReadOnly]
        public ShieldData DataOrigin;
        [SerializeField] [ReadOnly]
        private ShieldData _dataCurrent;

        public void Init(int rank, IBotDifficultyProvider botDifficultyData)
        {
            Rank = rank;
            
            DataOrigin = GetShieldData(rank, botDifficultyData);
            _dataCurrent = new ShieldData(DataOrigin);
        }

        public ShieldData DataCurrent
        {
            get => _dataCurrent;
            set
            {
                _dataCurrent = ShieldData.Clamp0(value);
            }
        }

        private static ShieldData GetShieldData(int rank, IBotDifficultyProvider botDifficultyData)
        {
            return new ShieldData
            {
                ChanceUseShield = botDifficultyData.GetValue("chanceUseShield", rank)
            };
        }
    }
} 