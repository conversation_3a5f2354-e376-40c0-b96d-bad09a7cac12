using System.Collections.Generic;
using Client.Common.Configs.Components.Effects;

namespace Client.FPV.Battle.Effects.Ai
{
    public class AiAdditionalEffectsProvider
    {
        public readonly Dictionary<EffectType, EffectIdData[]> AiEffects = new()
        {
            {
                EffectType.ChangeStaminaRecoverySpeed, new EffectIdData[]
                {
                    new EffectIdData(){Type = EffectType.AiChangeThrowSpeed, EffectDataId = 69023}
                }
            },
            {
                EffectType.ShieldDamageReductionModifier, new EffectIdData[]
                {
                    new EffectIdData(){Type = EffectType.AiChangeShieldUsage, EffectDataId = 69044}
                }
            },
            {
                EffectType.StaminaUsageSkip, new EffectIdData[]
                {
                    new EffectIdData(){Type = EffectType.AiChangeThrowSpeed, EffectDataId = 69023}
                }
            }
        };
    }
}