using System.Collections.Generic;
using System.Linq;
using Client.Common.AI.Data;
using Client.Common.Configs;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Effects;
using Client.Common.Configs.Extensions;
using Client.Common.CSV;
using Client.Common.Effects.Lifecycle.Components;
using Client.Common.Effects.Lifecycle.Controller;
using Client.Common.Ftue.Infrastructure;
using Client.Common.LocalLogic.FPV;
using Client.Common.Player.Controllers;
using Client.Common.Player.ProgressItems;
using Client.FPV.Battle.Components;
using Client.Utils.Extensions;
using Common;
using Leopotam.Ecs;
using Random = UnityEngine.Random;

namespace Client.FPV.Battle.Effects.Ai
{
    public class AiBottlesActivationSystem: IEcsInitSystem
    {
        private const string _BOTTLE_CHANCE_KEY_ = "bottle.activation-chance.{0}";
        private const string _START_DURATION_OFFSET_ = "bot.bottle.duration-offset.start";
        private const string _END_DURATION_OFFSET_ = "bot.bottle.duration-offset.end";
        private const string _BOOST_TIMER_DURATION_ = "srv.boost.duration";
        private const string _MAX_BOOST_COUNT_ = "srv.bottle.maxConcurrentCount";
        
        private readonly EcsFilter<FPVUnit, BattleAi> _aiUnits = default;

        private readonly EffectsController _effectsController = default;
        private readonly PlayerManager _playerManager = default;
        private readonly FtueProgress _ftueProgress = default;
        private readonly IBotDifficultyProvider _botDifficultyProvider = default;
        private readonly ConfigService _configService = default;
        private readonly GoogleDocsData _googleDocsData = default;

        private int _boostTimerDuration;
        private int _startOffset;
        private int _endOffset;
        
        private readonly AiAdditionalEffectsProvider _aiEffectProvider = new ();
        
        public void Init()
        {
            if (!_ftueProgress.Conditions.IsBotBottlesUnlocked())
            {
                return;
            }
            
            _boostTimerDuration = _googleDocsData.GetIntNumber(_BOOST_TIMER_DURATION_); //update after HAT-5826
            _startOffset = _googleDocsData.GetIntNumber(_START_DURATION_OFFSET_);
            _endOffset = _googleDocsData.GetIntNumber(_END_DURATION_OFFSET_);
            
            ActivateAiBottles();
        }

        private void ActivateAiBottles()
        {
            int maxConcurrentBoosts = (int) _googleDocsData.GetNumber(_MAX_BOOST_COUNT_);

            List<int> unlockedBottles = GetUnlockedBoostBottles();

            foreach (int index in _aiUnits)
            {
                int rank = _playerManager.Session.BattleResult.EnemyRank;
                int activatedBottles = 0;

                foreach (int bottleId in unlockedBottles)
                {
                    float activationChance = _botDifficultyProvider.GetValue(string.Format(_BOTTLE_CHANCE_KEY_, bottleId), rank, 0);

                    if (RandomX.IsLucky(activationChance, true))
                    {
                        ActivateEffects(_aiUnits.GetEntity(index), bottleId);
                        activatedBottles++;

                        if (activatedBottles == maxConcurrentBoosts)
                        {
                            break;
                        }
                    }
                }
            }
        }

        private List<int> GetUnlockedBoostBottles()
        {
            return _playerManager.Session.ProgressItems.GetForCurrentRegion()
                                 .Where(x => x.Type == ItemType.Bottle 
                                             && x.Status == ProgressItems.Status.Unlocked 
                                             && x.ItemId != PlayerInventory.Items.BOTTLE_FOR_HEAL)
                                 .Select(x => x.ItemId)
                                 .ToList();
        }
        
        private void ActivateEffects(EcsEntity unitEntity, int itemId)
        {
            foreach (EffectIdData effectIdData in _configService.GetByItemId<EffectKeysData>(itemId).Effects)
            {
                int randomEffectTime = GetRandomTime();
                ActivateEffect(effectIdData, randomEffectTime);
                if (_aiEffectProvider.AiEffects.TryGetValue(effectIdData.Type, out EffectIdData[] aiEffects))
                {
                    foreach (EffectIdData aiEffect in aiEffects)
                    {
                        ActivateEffect(aiEffect, randomEffectTime);
                    }
                }
            }

            void ActivateEffect(EffectIdData effectIdData, float currentDuration)
            {
                EcsEntity effectEntity = _effectsController.Activate(effectIdData, unitEntity);

                effectEntity.Get<TimerEffectMarker>();
                effectEntity.Get<EffectDurationData>().Construct(_boostTimerDuration);
                effectEntity.Get<EffectCurrentDuration>().Seconds = currentDuration;

                effectEntity.Get<AiTimerEffect>();
            }

            int GetRandomTime()
            {
                int randomTime = Random.Range(_startOffset, _boostTimerDuration - _endOffset);

                return randomTime;
            }
        }
    }
}