using Client.Common.Components.Common;
using Client.Common.Configs.Components.Effects;
using Client.Common.Effects.Lifecycle.Components;
using Client.Common.Effects.Lifecycle.Controller;
using Leopotam.Ecs;
using UnityEngine;

namespace Client.FPV.Battle.Effects.Ai
{
    public class AiEffectDeactivationByTimerSystem: IEcsRunSystem
    {
        private readonly EffectsController _effectsController = default;
        private readonly EcsFilter<ActiveMarker, TimerEffectMarker, EffectDurationData, EffectCurrentDuration, AiTimerEffect> _effects;
        
        public void Run()
        {
            foreach (int index in _effects)
            {
                ref EffectCurrentDuration currentDuration = ref _effects.Get4(index);
                currentDuration.Seconds += Time.unscaledDeltaTime;

                if (currentDuration.Seconds > _effects.Get3(index).Seconds)
                {
                    _effects.GetEntity(index);
                    _effectsController.Deactivate(_effects.GetEntity(index));
                }
            }
        }
    }
}