using Client.Common.Configs.Components.Ai;
using Client.Common.Effects.Lifecycle.Applying;
using Client.FPV.Battle.Components;
using Client.FPV.Battle.UnityComponents;
using Client.Utils.GameLogger;
using Leopotam.Ecs;

namespace Client.FPV.Battle.Effects.AiChangeThrowFrequency
{
    public class AiChangeThrowFrequencyApplier : IEffectApplier
    {
        public void Apply(EcsEntity targetEntity, EcsEntity effectEntity)
        {
            if (!targetEntity.Has<BattleAi>())
            {
                return;
            }

            ref ThrowFrequencyData effectData = ref effectEntity.Get<ThrowFrequencyData>();
            
            AiThrowData throwData = targetEntity.Get<BattleAi>().ThrowData;

            throwData.ThrowFrequencyDataModifier += effectData;

            ResetThrowSequence(throwData);
        }

        private void ResetThrowSequence(AiThrowData throwData)
        {
            throwData.CurrentThrowSequenceLength = 0;
        }
    }
}