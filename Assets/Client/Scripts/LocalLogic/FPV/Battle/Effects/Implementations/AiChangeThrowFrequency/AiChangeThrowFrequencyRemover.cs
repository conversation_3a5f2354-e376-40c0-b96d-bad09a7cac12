using Client.Common.Configs.Components.Ai;
using Client.Common.Effects.Lifecycle.Components;
using Client.Common.Effects.Lifecycle.Removing;
using Client.FPV.Battle.Components;
using Client.FPV.Battle.UnityComponents;
using Client.Utils.Extensions.EcsExtensions;
using Client.Utils.GameLogger;
using Leopotam.Ecs;

namespace Client.FPV.Battle.Effects.AiChangeThrowFrequency
{
    public class AiChangeThrowFrequencyRemover : IEffectRemover
    {
        public void Remove(EcsEntity effectEntity, EcsEntity effectStorage)
        {
            EcsEntity targetEntity = effectEntity.Get<TargetEntityHolder>().Entity;
            if (!targetEntity.HasAlive<BattleAi>())
            {
                return;
            }

            ref ThrowFrequencyData effectData = ref effectEntity.Get<ThrowFrequencyData>();
            
            AiThrowData throwData = targetEntity.Get<BattleAi>().ThrowData;

            throwData.ThrowFrequencyDataModifier -= effectData;
        }
    }
}