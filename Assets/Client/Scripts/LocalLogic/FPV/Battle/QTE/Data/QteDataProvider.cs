using System;
using System.Collections.Generic;
using Client.Common.Configs.Components.Effects;

namespace Client.FPV.Battle.QTE.Data
{
    //todo: Replace with parser of Qte sheet
    public class QteDataProvider
    {
        private readonly Dictionary<QteId, QteData> _eventsMap = new()
        {
            {
                QteId.Freeze,
                new QteData(
                    QteId.Freeze,
                    new[] {new EffectIdData().Construct(EffectType.Freeze, 69070)},
                    new[]
                    {
                        new EffectIdData().Construct(EffectType.Freeze, 69070),
                        new EffectIdData().Construct(EffectType.AiChangeShieldUsage, 69041)
                    })
            },
            {
                QteId.IncreaseMovementSpeed,
                new QteData(
                    QteId.IncreaseMovementSpeed,
                    new[] {new EffectIdData().Construct(EffectType.SpeedupMovement, 69060)},
                    new[]
                    {
                        new EffectIdData().Construct(EffectType.SpeedupMovement, 69060),
                        new EffectIdData().Construct(EffectType.AiChangeShieldUsage, 69042)
                    })
            },
            {
                QteId.Fatigue,
                new QteData(
                    QteId.Fatigue,
                    new[] {new EffectIdData().Construct(EffectType.ChangeThrowCost, 69050)},
                    new[]
                    {
                        new EffectIdData().Construct(EffectType.ChangeThrowCost, 69050),
                        new EffectIdData().Construct(EffectType.AiChangeShieldUsage, 69043)
                    })
            },
            {
                QteId.InfiniteStamina,
                new QteData(
                    QteId.InfiniteStamina,
                    new[] {new EffectIdData().Construct(EffectType.InfiniteStamina, 69010)},
                    new[]
                    {
                        new EffectIdData().Construct(EffectType.InfiniteStamina, 69010),
                        new EffectIdData().Construct(EffectType.AiChangeThrowSpeed, 69020),
                        new EffectIdData().Construct(EffectType.AiChangeShieldUsage, 69040)
                    })
            }
        };

        public QteData Get(QteId id)
        {
            if (_eventsMap.TryGetValue(id, out QteData qteData))
            {
                return qteData;
            }

            throw new Exception($"There's no qte event data for {id}");
        }
    }
}