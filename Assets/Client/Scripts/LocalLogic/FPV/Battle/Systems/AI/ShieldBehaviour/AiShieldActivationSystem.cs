using Client.Common.AI.BehaviourTree.Runtime.NodeSystems;
using Client.Common.AI.BehaviourTree.Runtime.Scripts.Components;
using Client.Common.AI.BehaviourTree.Runtime.Scripts.Data;
using Client.FPV.Battle.Components;
using Client.FPV.Battle.Components.AI;
using Client.FPV.Battle.ShieldBehaviour.Defence.Base.Components;
using Client.Utils.Extensions;
using Client.Utils.GameLogger;
using Leopotam.Ecs;

namespace Client.FPV.Battle.Systems.AI.ShieldBehaviour
{
    internal class AiShieldActivationSystem : FreezableAiSystem<AiUnitShield>
    {
        protected override NodeState OnStartInternal(ref EcsEntity nodeEntity, ref AiUnitShield nodeComponent)
        {
            EcsEntity agentEntity = nodeEntity.Get<AgentEntityComponent>().Agent;
            
            if (agentEntity.Has<ShieldBlock>())
            {
                return NodeState.Failure;
            }

            if (!agentEntity.Has<BattleAi>() || !agentEntity.Has<Shield>())
            {
                GameLogger.LogError("[AI] no shield or shield data on bot entity");
                return NodeState.Failure;
            }
            
            if (agentEntity.Get<Shield>().Enabled)
            {
                return NodeState.Success;
            }
            
            if (RandomX.IsLucky(agentEntity.Get<BattleAi>().ShieldData.DataCurrent.ChanceUseShield))
            {
                agentEntity.Get<ShieldActivateRequest>();
                return NodeState.Success;
            }
            else
            {
                return NodeState.Failure;
            }
        }

        protected override NodeState OnUpdateInternal(ref EcsEntity nodeEntity, ref AiUnitShield nodeComponent)
        {
            return NodeState.Failure;
        }
    }
}