using Client.Common.Configs.Components;
using Client.Common.CSV;
using Client.Common.LocalLogic.FPV;
using Client.Common.Traumas.Effects;
using Client.FPV.Battle.Components;
using Client.FPV.Battle.ShieldBehaviour.Defence.Base.Components;
using Client.FPV.Battle.Stamina.Components;
using Client.FPV.Battle.Systems.Shurikens;
using Client.FPV.Battle.UnityComponents;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.Extensions;
using Leopotam.Ecs;
using UnityEngine;

namespace Client.FPV.Battle.Systems.AI.Throw.ThrowTime
{
    internal class ThrowTimeProvider
    {
        private const float _MIN_THROW_PERIOD_ = 1f;
        private const float _MAX_THROW_PERIOD_ = 1.5f;

        private readonly EcsFilter<ShurikenDelayedSpawnEvent> _delayedThrows;

        private readonly float _bodyTraumaBurstDelay;
        private readonly float _armTraumaThrowDelay;

        public ThrowTimeProvider(LocalEcsWorld world, GoogleDocsData googleDocsData)
        {
            _delayedThrows = world.GetFilter<EcsFilter<ShurikenDelayedSpawnEvent>>();
            
            _bodyTraumaBurstDelay = googleDocsData.GetNumber("battle.bot.wound.body.shurikenBurstsOffset");
            _armTraumaThrowDelay = googleDocsData.GetNumber("battle.bot.wound.arm.shurikenThrowsOffset");
        }

        public bool IsTimeToThrow(FPVUnit aiUnit)
        {
            AiThrowData aiThrowData = aiUnit.Entity.Get<BattleAi>().ThrowData;

            return Time.time >= aiThrowData.NextThrowTime;
        }

        public void InitNextThrowTime(BattleAi battleAi)
        {
            AiThrowData aiThrowData = battleAi.ThrowData;
            ResetSequence(aiThrowData);
            aiThrowData.NextThrowTime = Time.time + GetNextThrowTime(aiThrowData.ThrowFrequencyData.MultipleThrowsFrequency);
        }

        public void UpdateNextThrowTime(FPVUnit aiUnit, FPVUnit playerUnit)
        {
            AiThrowData aiThrowData = aiUnit.Entity.Get<BattleAi>().ThrowData;

            float? nextThrowTime = GetThrowTimeByStaminaLack(aiUnit) ??
                                   GetThrowTimeDependsOnPlayerShield(aiUnit, playerUnit) ??
                                   GetThrowTimeByNewSequence(aiUnit) ??
                                   GetThrowTimeByNewThrow(aiUnit);

            if (nextThrowTime.HasValue)
            {
                aiThrowData.NextThrowTime = nextThrowTime.Value;
            }
        }

        private float? GetThrowTimeByStaminaLack(FPVUnit aiUnit)
        {
            ref UnitStamina stamina = ref aiUnit.Entity.Get<UnitStamina>();

            float nextThrowCost = stamina.ThrowCost;
            float delayedThrowStaminaCost = _delayedThrows.GetEntitiesCount() * stamina.ThrowCost;
            float totalThrowCost = nextThrowCost + delayedThrowStaminaCost;
            
            AiThrowData aiThrowData = aiUnit.Entity.Get<BattleAi>().ThrowData;
            AiStaminaData aiStaminaData = aiThrowData.AiStaminaData;

            if (stamina.Value - totalThrowCost <= 0 &&
                RandomX.IsLucky(aiStaminaData.StaminaSaveChance))
            {
                ResetSequence(aiThrowData);

                float staminaPeriod = stamina.ThrowCost / stamina.RecoveryData.RecoverySpeed;

                return Time.time + GetNextThrowTimeAfterStaminaLack(staminaPeriod, stamina.MaxValue, aiStaminaData.StaminaWaitPeriodsAfterLackMin, aiStaminaData.StaminaWaitPeriodsAfterLackMax);
            }

            return null;
        }

        private float? GetThrowTimeDependsOnPlayerShield(FPVUnit aiUnit, FPVUnit playerUnit)
        {
            AiThrowData aiThrowData = aiUnit.Entity.Get<BattleAi>().ThrowData;
            UnitStamina aiStamina = aiUnit.Entity.Get<UnitStamina>();

            bool playerShieldEnabled = playerUnit.Entity.Get<Shield>().Enabled;
            bool playerShieldWasEnabled = aiThrowData.PlayerShieldWasEnabled;
            aiThrowData.PlayerShieldWasEnabled = playerShieldEnabled;

            if (PlayerRecentlyEnabledShield())
            {
                aiThrowData.PlayerShieldActivationTime = Time.time;
            }
            else if (PlayerRecentlyDisabledShield())
            {
                ResetSequence(aiThrowData);

                return Time.time + aiThrowData.ThrowFrequencyData.ThrowReactionDelay;
            }

            if (PlayerShieldStillEnabled())
            {
                // we don't care about throw sequences if player shield is enabled
                float staminaPeriod = aiStamina.ThrowCost / aiStamina.RecoveryData.RecoverySpeed;
                float multipleThrowsPeriod = GetAverageThrowPeriod(aiThrowData.ThrowFrequencyData.MultipleThrowsFrequency);
                float clampedStaminaPeriod = Mathf.Clamp(staminaPeriod, multipleThrowsPeriod, float.MaxValue);

                aiThrowData.CurrentThrowIndex++;

                return Time.time + GetNextThrowTimeByStamina(clampedStaminaPeriod, aiThrowData);
            }

            return null;
            bool PlayerRecentlyEnabledShield() => !playerShieldWasEnabled && playerShieldEnabled;
            bool PlayerRecentlyDisabledShield() => playerShieldWasEnabled && !playerShieldEnabled;
            bool PlayerShieldStillEnabled() => playerShieldEnabled && Time.time > aiThrowData.PlayerShieldActivationTime + aiThrowData.ThrowFrequencyData.ThrowReactionDelay;
        }

        private float? GetThrowTimeByNewSequence(FPVUnit aiUnit)
        {
            AiThrowData aiThrowData = aiUnit.Entity.Get<BattleAi>().ThrowData;

            if (aiThrowData.CurrentThrowIndex >= aiThrowData.CurrentThrowSequenceLength)
            {
                float burstDelay = GetBurstDelay(aiUnit.Entity);

                CreateNextThrowSequence(aiThrowData);

                return Time.time + burstDelay + GetNextThrowTime(aiThrowData.ThrowFrequencyData.SingleThrowFrequency);
            }

            return null;
        }

        private float? GetThrowTimeByNewThrow(FPVUnit aiUnit)
        {
            AiThrowData aiThrowData = aiUnit.Entity.Get<BattleAi>().ThrowData;

            float throwsDelay = GetThrowDelay(aiUnit.Entity);

            aiThrowData.CurrentThrowIndex++;

            return Time.time + throwsDelay + GetNextThrowTime(aiThrowData.CurrentThrowSequenceFrequency);
        }

        public void ResetSequence(AiThrowData aiThrowData)
        {
            aiThrowData.CurrentThrowSequenceLength = 0;
            CreateNextThrowSequence(aiThrowData);
        }

        private void CreateNextThrowSequence(AiThrowData aiThrowData)
        {
            int newSequenceLength = Random.Range(2, aiThrowData.ThrowFrequencyData.MaxThrowSequenceLength + 1);
            aiThrowData.CurrentThrowSequenceFrequency = newSequenceLength == 1 ? aiThrowData.ThrowFrequencyData.SingleThrowFrequency : aiThrowData.ThrowFrequencyData.MultipleThrowsFrequency;
            aiThrowData.CurrentThrowSequenceLength = newSequenceLength;
            aiThrowData.CurrentThrowIndex = 0;
        }

        private float GetNextThrowTime(float throwFrequency) => Random.Range(_MIN_THROW_PERIOD_, _MAX_THROW_PERIOD_) / throwFrequency;

        private float GetAverageThrowPeriod(float throwFrequency) => (_MIN_THROW_PERIOD_ + _MAX_THROW_PERIOD_) * 0.5f / throwFrequency;

        private float GetNextThrowTimeByStamina(float staminaPeriod, AiThrowData aiThrowData) => staminaPeriod + Random.Range(-aiThrowData.ThrowFrequencyData.ThrowReactionDelay, aiThrowData.ThrowFrequencyData.ThrowReactionDelay) / 2f;

        private float GetNextThrowTimeAfterStaminaLack(float staminaPeriod, float maxStamina, int minWaitStaminaPeriodsAfterLack, int maxWaitStaminaPeriodsAfterLack)
        {
            int waitStaminaPeriodsCount = Random.Range(minWaitStaminaPeriodsAfterLack, maxWaitStaminaPeriodsAfterLack);
            float staminaPeriodsWait = staminaPeriod * waitStaminaPeriodsCount;
            return Mathf.Max(staminaPeriodsWait, maxStamina);
        }

        private float GetBurstDelay(EcsEntity unitEntity)
        {
            return unitEntity.Has<EffectWoundBody>()
                ? _bodyTraumaBurstDelay
                : 0;
        }

        private float GetThrowDelay(EcsEntity unitEntity)
        {
            return unitEntity.Has<EffectWoundArm>()
                ? _armTraumaThrowDelay
                : 0;
        }
    }
}