using System.Threading;
using Client.Common;
using Client.Common.Configs;
using Client.Common.CSV;
using Client.Common.Durability;
using Client.Common.EcsFreeze;
using Client.Common.Ftue.Infrastructure;
using Client.Common.Items;
using Client.Common.Items.Icons;
using Client.Common.Loadout.Services;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Purchases.Controllers;
using Client.Common.SceneLoading;
using Client.Common.SceneLoading.SceneData.Abstractions;
using Client.Common.SceneLoading.SceneData.Implementations;
using Client.Common.SceneLoading.SceneProviders;
using Client.Common.ScreenTransitions;
using Client.Common.TimeGiver.Abstractions;
using Client.FPV.Battle.BattleResultScroll;
using Client.FPV.Battle.BattleResultScroll.Loadout;
using Client.FPV.Battle.Components;
using Client.FPV.Battle.Systems.Analytics;
using Client.FPV.Battle.Systems.Ending.Factories;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.Extensions;
using Client.Utils.ResourceLoading;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using Leopotam.Localization;
using UnityEngine;
using Result = Client.Common.Network.MetaNet.BattleResult;

namespace Client.FPV.Battle.Systems.Ending
{
    public struct BattleResultFreeze : IFreezeMarkerComponent
    {
    }

    public struct BattleResultTransition : IEcsIgnoreInFilter
    {
    }

    internal sealed class BattleResultTransitionSystem : IEcsRunSystem, IEcsDestroySystem
    {
        private readonly LocalEcsWorld _world = default;
        private readonly AnimatedSceneLoader _sceneLoader = default;
        private readonly BattleResultScrollFactory _battleResultScrollFactory = default;
        private readonly CancellationTokenSource _cts = default;
        
        private readonly EcsFilter<BattleResultComponent, BattleResultTransition> _battleResultTransition = default;
        private readonly EcsFilter<BattleResultRequestsProcessed> _battleResultProcessedRequests;

        private readonly BattleFeatureConfig _battleFeatureConfig;
        private readonly Transform _battleResultViewRoot;

        private bool _transitionStarted;

        private bool _battleResultTransitionStepCompleted;

        public BattleResultTransitionSystem(BattleFeatureConfig battleFeatureConfig, Transform battleResultViewRoot)
        {
            _battleFeatureConfig = battleFeatureConfig;
            _battleResultViewRoot = battleResultViewRoot;
        }

        public void Run()
        {
            if (!_transitionStarted && !_battleResultTransition.IsEmpty() && IsBattleResultRequestProcessed())
            {
                _transitionStarted = true;
                EcsEntity freeze = _world.NewEntity();
                freeze.Get<Freeze>();
                freeze.Get<BattleResultFreeze>();
                LoadBattleResultScroll(_battleResultTransition.Get1(0).Result);
            }
        }

        private bool IsBattleResultRequestProcessed()
        {
            if (!_battleFeatureConfig.Offline)
            {
                return !_battleResultProcessedRequests.IsEmpty();
            }

            return true;
        }

        private void LoadBattleResultScroll(Result battleResult)
        {
            ManualNextSceneProvider nextSceneProvider = new(GetNextScene(battleResult));

            if (battleResult == Result.Lose)
            {
                DisplayLoseScroll(nextSceneProvider, _cts.Token).Forget();
            }
            else if (battleResult == Result.Win)
            {
                DisplayWinScroll(nextSceneProvider, _cts.Token).Forget();
            }
        }

        private async UniTaskVoid DisplayLoseScroll(ManualNextSceneProvider nextSceneProvider, CancellationToken token)
        {
            BattleResultScrollController scrollController = await _battleResultScrollFactory.CreateLose(_battleResultViewRoot, nextSceneProvider, token);

            await DisplayScroll(scrollController, nextSceneProvider, token);
        }

        private async UniTask DisplayWinScroll(ManualNextSceneProvider nextSceneProvider, CancellationToken token)
        {
            BattleResultScrollController scrollController = await _battleResultScrollFactory.CreateWin(_battleResultViewRoot, token);

            await DisplayScroll(scrollController, nextSceneProvider, token);
        }

        private async UniTask DisplayScroll(BattleResultScrollController scrollController, ManualNextSceneProvider nextSceneProvider, CancellationToken token)
        {
            if (!scrollController.HasScrollItems())
            {
                LoadNextScene(nextSceneProvider);

                return;
            }

            _sceneLoader.TransitionService.FadeOut(cancellationToken: token);

            await scrollController.Open();
            
            LoadNextScene(nextSceneProvider);
        }
        
        private ISceneLoadData GetNextScene(Result battleResult)
        {
            if (battleResult == Result.Win)
            {
                return new RegionSceneLoadData();
            }

            return new RegionResultSceneLoadData();
        }

        private void LoadNextScene(ISceneProvider sceneProvider)
        {
            if (_sceneLoader.SceneLoader.GetPrevious().SceneName == Idents.Scenes.Market)
            {
                _sceneLoader.BackInHistory();
            }
            else
            {
                _sceneLoader.Load(sceneProvider.GetNextSceneData(), historyMode: SceneHistoryMode.ClearHistory);
            }
        }

        public void Destroy()
        {
            _cts?.CancelAndDispose();
        }
    }
}