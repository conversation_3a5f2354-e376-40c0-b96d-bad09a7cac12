using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Client.Common.CSV;
using Client.Common.Ftue.Infrastructure;
using Client.Common.Items;
using Client.Common.Items.Icons;
using Client.Common.Loadout.Traumas;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Purchases.Controllers;
using Client.Common.SceneLoading.SceneProviders;
using Client.Common.TimeGiver.Abstractions;
using Client.FPV.Battle.BattleResultScroll;
using Client.FPV.Battle.BattleResultScroll.Loadout;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.ResourceLoading;
using Common;
using Cysharp.Threading.Tasks;
using Leopotam.Localization;
using UnityEngine;

namespace Client.FPV.Battle.Systems.Ending.Factories
{
    public class BattleResultScrollFactory
    {
        private readonly PlayerManager _playerManager;
        private readonly LocalEcsWorld _world;
        private readonly ResourceLoadingService _resourceLoadingService;
        private readonly QuantifiedIconLoadingService _quantifiedIconLoadingService;
        private readonly CsvLocalization _localization;
        private readonly ItemData _itemData;
        private readonly ITimeGiver _timeGiver;
        private readonly MetaNet _metaNet;
        private readonly GoogleDocsData _googleDocsData;
        private readonly LoadoutBattleResultController _loadoutBattleResultController;
        
        private readonly ItemPurchaseController _purchaseController;
        private readonly LoadoutTraumasController _loadoutTraumasController;
        private readonly FtueProgress _ftueProgress;

        public BattleResultScrollFactory(
            PlayerManager playerManager,
            LocalEcsWorld world,
            ResourceLoadingService resourceLoadingService,
            QuantifiedIconLoadingService quantifiedIconLoadingService,
            CsvLocalization localization,
            ItemData itemData,
            ITimeGiver timeGiver,
            MetaNet metaNet,
            GoogleDocsData googleDocsData, 
            LoadoutBattleResultController loadoutBattleResultController, 
            ItemPurchaseController purchaseController, 
            LoadoutTraumasController loadoutTraumasController, 
            FtueProgress ftueProgress)
        {
            _playerManager = playerManager;
            _world = world;
            _resourceLoadingService = resourceLoadingService;
            _quantifiedIconLoadingService = quantifiedIconLoadingService;
            _localization = localization;
            _itemData = itemData;
            _timeGiver = timeGiver;
            _metaNet = metaNet;
            _googleDocsData = googleDocsData;
            _loadoutBattleResultController = loadoutBattleResultController;
            _purchaseController = purchaseController;
            _loadoutTraumasController = loadoutTraumasController;
            _ftueProgress = ftueProgress;
        }

        public UniTask<BattleResultScrollController> CreateWin(Transform root, CancellationToken token)
        {
            List<IBattleResultViewController> controllers = CreateCommon(token);
            
            return CreateScrollController(root, controllers, token);
        }

        public UniTask<BattleResultScrollController> CreateLose(Transform root, ManualNextSceneProvider nextSceneProvider, CancellationToken token)
        {
            List<IBattleResultViewController> controllers = CreateCommon(token);
            controllers.Add(new DefeatViewController(_timeGiver, nextSceneProvider, token));

            return CreateScrollController(root, controllers, token);
        }

        private List<IBattleResultViewController> CreateCommon(CancellationToken token) => new()
        {
            new HealControllerAdapter(_loadoutTraumasController, _ftueProgress),
            new BrokenViewController(_itemData, ItemType.Outfit, token, _purchaseController),
            new BrokenViewController(_itemData, ItemType.ShurikenSkin, token, _purchaseController),
            _loadoutBattleResultController,
        };

        private UniTask<BattleResultScrollController> CreateScrollController(Transform root, List<IBattleResultViewController> controllers, CancellationToken token)
        {
            foreach (IBattleResultViewController battleResultViewController in controllers)
            {
                if (battleResultViewController is BattleResultViewControllerBase controller)
                {
                    controller.Init(_resourceLoadingService, _quantifiedIconLoadingService, _metaNet, _playerManager, _googleDocsData, _world, _localization);
                }
            }
            
            return CreateContainer(root, controllers, token);
        }

        private async UniTask<BattleResultScrollController> CreateContainer(
            Transform root, 
            ICollection<IBattleResultViewController> controllers, 
            CancellationToken token)
        {
            ResourceConfig resourceConfig = new()
            {
                Path = BattleResultIdents.Paths.BATTLE_RESULT_CONTAINER_VIEW_PATH,
                LoadFrom = LoadFromType.Ressources
            };
            
            BattleResultContainerView containerView = await _resourceLoadingService.Get(resourceConfig).LoadUniAsync<BattleResultContainerView>(token);
            BattleResultScrollController scrollController = new(Object.Instantiate(containerView, root), token);
            
            await scrollController.Init(controllers.AsEnumerable());

            return scrollController;
        }
    }
}