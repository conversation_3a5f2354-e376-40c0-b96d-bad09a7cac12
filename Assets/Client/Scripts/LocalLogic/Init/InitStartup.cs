using System.Threading;
using System.Threading.Tasks;
using Client.Common;
using Client.Common.Analytics.Infrastructure.Services;
using Client.Common.Analytics.Systems;
using Client.Common.AppVersion;
using Client.Common.Audio;
using Client.Common.ECS;
using Client.Common.SceneLoading;
using Client.Common.SceneLoading.Analytics;
using Client.Common.SceneLoading.Analytics.Data;
using Client.Common.SceneLoading.SceneData.Implementations;
using Client.Common.ScreenTransitions;
using Client.Common.Services;
using Client.Common.UI.InputLockService;
using Client.Common.Visual;
using Client.Utils.ApplicationStateHelper;
using Client.Utils.Extensions;
using Client.Utils.GameLogger;
using Client.Utils.ServiceTool;
using Cysharp.Threading.Tasks;
using JetBrains.Annotations;
using Leopotam.Ecs;
using Plugins.iOS.iOSAtt.Scripts;
using UnityEngine;
using UnityEngine.SceneManagement;
using VContainer.Unity;

namespace Client.Init
{
    internal sealed class InitStartup : MonoBehaviour
    {
        [Header("Global")]
        [SerializeField] private TransitionView _transitionView;
        [SerializeField] private FullScreenLocker _lockerCanvas;
        [SerializeField] private Transform _dontDestroyViewRoot;

        [SerializeField] private GlobalAudioPack _globalAudioPack;
        
        [SerializeField] private GlobalVisual _globalVisual;
        [SerializeField] private ContextScreenManager _contextScreenManager;

        private EcsWorld _world;
        private ServiceRegistrator _serviceRegistrator;
        private CancellationTokenSource _cts;
        
        private InitSceneAnalyticsTracker _tracker;
        
        private async void Start()
        {
            _cts = _cts.CancelAndCreate();
            
            Application.targetFrameRate = 200;             // Screen.currentResolution.refreshRate doesn't work as expected https://discussions.unity.com/t/90-120hz-support/880088/5
            Screen.sleepTimeout = SleepTimeout.NeverSleep; // disable screen dimming.
            ApplicationStateHelper.Init();

            DontDestroyOnLoad(_dontDestroyViewRoot.gameObject);

            GlobalWorldCreator globalWorldCreator = new();
            _world = globalWorldCreator.GetWorld();


            _serviceRegistrator = new ServiceRegistrator(globalWorldCreator, _globalAudioPack, _globalVisual, _transitionView, _lockerCanvas, _dontDestroyViewRoot);
            _serviceRegistrator.Register();

            AnalyticsService analyticsService = Service<AnalyticsService>.Get();
            _tracker = new InitSceneAnalyticsTracker(analyticsService);
            FireAnalytics(LoadingAnalyticsEnum.InitSceneStart);
            
            LocalizationDefaultLoader localizationLoader = new();
            await localizationLoader.Load();

            FireAnalytics(LoadingAnalyticsEnum.InitSceneSuccess);

#if DEBUG_PANEL
            await LoadDebugPanel();
#endif

#if UNITY_IOS
            AppTrackTransparency();
#else
            ValidateAndConnect();
#endif
        }

        private async UniTask LoadDebugPanel()
        {
            if (!Application.CanStreamedLevelBeLoaded(Idents.Scenes.DEBUG))
            {
                GameLogger.LogWarning("Debug scene can't be loaded");

                return;
            }

            using (LifetimeScope.EnqueueParent(_serviceRegistrator.GlobalScopeService.Scope))
            {
                await SceneManager.LoadSceneAsync(Idents.Scenes.DEBUG, LoadSceneMode.Additive).ToUniTask();
            }
        }

        [UsedImplicitly]
        private void AppTrackTransparency()
        {
            _contextScreenManager.AttRequestFinish += ValidateAndConnect;
            _contextScreenManager.Init();
        }

        private async void ValidateAndConnect()
        {
#if PRODUCTION
            if (await ValidateVersion() == false)
            {
                return;
            }
#endif
            Service<SceneLoader>.Get().Load(new ConnectionSceneLoadData());
            FireAnalytics(LoadingAnalyticsEnum.ConnectSceneSuccess);
        }

        [UsedImplicitly]
        private async Task<bool> ValidateVersion()
        {
            if (false == await AppVersionProvider.IsClientVersionValid(_cts.Token))
            {
                Service<SceneLoader>.Get().Load(new VersionValidationSceneLoadData());
                return false;
            }

            return true;
        }

        private void FireAnalytics(LoadingAnalyticsEnum analyticsEnum)
        {
            LoadingAnalyticsEvent eventData = new()
            {
                EventType = analyticsEnum,
                SceneName = "Init"
            };
            _tracker.SetEvent(eventData);
            _tracker.Track();
        }

        private void OnDestroy()
        {
            _cts.CancelAndDispose();
        }
    }
}