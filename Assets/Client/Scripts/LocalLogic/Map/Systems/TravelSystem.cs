using System.Threading;
using Client.Common.Audio;
using Client.Common.Audio.Banks;
using Client.Common.CSV;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.SceneLoading;
using Client.Common.SceneLoading.SceneData.Implementations;
using Client.Common.ScreenTransitions;
using Client.Common.ScreenTransitions.Views;
using Client.Common.Services.Ads;
using Client.Common.Services.Ads.Analytics;
using Client.Common.Services.Ads.Infrastructure;
using Client.Common.UI.Buttons.Abstract;
using Client.Map.Components;
using Client.Map.Services;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.StateSwitcher;
using Common;
using Leopotam.Ecs;
using Leopotam.Localization;
using UnityEngine;

namespace Client.Map.Systems
{
    internal sealed class TravelSystem : IEcsInitSystem, IEcsDestroySystem
    {
        private const string _MIN_ROOM_CAPACITY_KEY_ = "room.search.bot.count.min";
        private const string _MAX_ROOM_CAPACITY_KEY_ = "room.search.bot.count.max";
        private const string _ADS_MINIMAP_X2_KEY_ = "ads.minimap.x2_2.21";

        private readonly LocalEcsWorld _world = default;
        private readonly MetaNet _metaNet = default;
        private readonly PlayerManager _playerManager = default;
        private readonly LocalSharedState _localSharedState = default;
        private readonly GoogleDocsData _googleDocsData = default;
        private readonly IntermediateAudio _intermediateAudio = default;
        private readonly CsvLocalization _csvLocalization = default;
        private readonly AnimatedSceneLoader _sceneLoader = default;

        private readonly CancellationToken _token;

        private readonly IButton _travelButton;
        private readonly TheAdButton _adButton;
        private Vector2 _touchPoint;

        public TravelSystem(IButton travelButton, TheAdButton adButton)
        {
            _travelButton = travelButton;
            _adButton = adButton;
        }

        public void Init()
        {
            InitAdButton();
            _travelButton.Clicked += OnTravelBtnPressed;
        }

        private void InitAdButton()
        {
            _adButton.Clicked += SendAdsAnalyticsClicked;
            _adButton.AdsStarted += SendAdsAnalyticsStarted;
            _adButton.AdsFinished += SendAdsAnalyticsFinished;
            _adButton.AdsFinished += OnAdsButtonTravel;
            _adButton.AdReady += OnAdsReady;
            _adButton.SetText(_csvLocalization.Get(_ADS_MINIMAP_X2_KEY_));
        }

        private void OnTravelBtnPressed()
        {
            _localSharedState.UiLocked = true;
            _world.NewEntity().Get<SoundPoint>().SoundAssetPath = _intermediateAudio.TravelToRegion;
            _playerManager.Session.RegionId = _localSharedState.CurrentMapId;
            _playerManager.Session.RegionExitId = _localSharedState.CurrentMap.View.GetRandomEntryId();
            _playerManager.Session.RegionResultData.Reset();
            _playerManager.Session.RegionSessionData.Setup();
            ref MapRealignEvent realign = ref _world.NewEntity().Get<MapRealignEvent>();
            realign.Instant = true;
            realign.Id = _localSharedState.CurrentMapId;
            _localSharedState.UiLocked = true;
            Travel();
        }

        private async void OnAdsButtonTravel(AdsResult adsResult)
        {
            if (adsResult != AdsResult.Finished)
            {
                return;
            }

            OnTravelBtnPressed();

            ItemComponent bloodColbaAds = new()
            {
                Id = PlayerInventory.Items.BLOOD_COLBA_ADS,
                Count = 1
            };
            await _metaNet.AddItemToInventory(bloodColbaAds, _token);
        }

        private void Travel()
        {
            LoadRoom();

            void LoadRoom()
            {
                uint foundRoomMinBotCount = (uint) _googleDocsData.GetNumber(_MIN_ROOM_CAPACITY_KEY_);
                uint foundRoomMaxBotCount = (uint) _googleDocsData.GetNumber(_MAX_ROOM_CAPACITY_KEY_);
                uint foundRoomBotCount = (uint) Random.Range(foundRoomMinBotCount, foundRoomMaxBotCount);
                _playerManager.Session.RegionSessionData.BotData.StartBotCount = foundRoomBotCount;

                _sceneLoader.LoadWithTransition(
                    new RoomViewTransitionView(foundRoomBotCount, foundRoomMaxBotCount, _csvLocalization), 
                    new RegionSceneLoadData());
            }
        }

        private void OnAdsReady()
        {
            _adButton.SetState(TheActiveState.Active);
        }
        
        private void SendAdsAnalyticsClicked()
        {
            SendAdsAnalyticsRequest(AdsResult.Skipped, AdsActions.Click);
        }
        
        private void SendAdsAnalyticsStarted()
        {
            SendAdsAnalyticsRequest(AdsResult.Skipped, AdsActions.Start);
        }

        private void SendAdsAnalyticsFinished(AdsResult adsResult)
        {
            if (adsResult != AdsResult.Finished)
            {
                return;
            }
            
            SendAdsAnalyticsRequest(AdsResult.Finished, AdsActions.Finish);
        }

        private void SendAdsAnalyticsRequest(AdsResult showResult, AdsActions action = AdsActions.None)
        {
            _world.NewEntity().Get<AdsAnalyticsRequest>().Construct(AdsIdents.PROMO_NONE_ID, "map_travel", showResult, action);
        }

        public void Destroy()
        {
            _travelButton.Clicked -= OnTravelBtnPressed;
            _adButton.Clicked -= SendAdsAnalyticsClicked;
            _adButton.AdsStarted -= SendAdsAnalyticsStarted;
            _adButton.AdsFinished -= OnAdsButtonTravel;
            _adButton.AdReady -= OnAdsReady;
        }
    }
}