using System.Collections.Generic;
using System.Threading;
using Client.Common.Audio;
using Client.Common.GoTo;
using Client.Common.Items;
using Client.Common.Items.Icons;
using Client.Common.Items.QuantityProviders;
using Client.Common.Quests;
using Client.Common.Quests.GoTo;
using Client.Common.TimeGiver.Abstractions;
using Client.Common.UI.InputLockService;
using Client.Common.UI.Rewards.Core;
using Client.Common.UI.Tabs;
using Client.Prizes.DailyQuests.Controllers;
using Client.Prizes.DailyQuests.Data;
using Client.Prizes.DailyQuests.Events;
using Client.Prizes.DailyQuests.Factories;
using Client.Utils.ECS.ECSAsyncInitSystem;
using Client.Utils.ResourceLoading;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using Leopotam.Localization;

namespace Client.Prizes.DailyQuests.Systems
{
    public class DailyQuestsTabSystem : IEcsRunSystem, IEcsAsyncInitSystem, IEcsDestroySystem
    {
        private readonly ItemData _itemData = default;
        private readonly QuantifiedIconLoadingService _quantifiedIconLoadingService = default;
        private readonly QuestsData _questsData = default;
        private readonly CsvLocalization _localization = default;
        private readonly QuantifiedLocalizationService _quantifiedLocalization = default;
        private readonly ITimeGiver _timeGiver = default;
        private readonly GoToService _goToService = default;
        private readonly DailyQuestsTabController _dailyQuestsTabController = default;

        private readonly EcsFilter<ChangeTabRequest> _tabRequests = default;
        private readonly EcsFilter<MasterRewardClaimed> _masterRewardClaimed = default;
        private readonly EcsFilter<UpdateQuestsRequest> _requestQuestsUpdate = default;

        private readonly DailyQuestsColorConfig _colorConfig;
        private CancellationToken _cancellationToken;

        private DailyQuestsTimerController _timerController;
        private DailyQuestListModelFactory _modelFactory;

        private readonly DailyQuestTabBundle _dailyQuestTabBundle;
        private readonly ResourceLoadingService _resourceLoadingService;
        private readonly FullScreenLocker _screenLocker;
        private readonly UISoundHelper _uiSoundHelper;

        public DailyQuestsTabSystem(DailyQuestsColorConfig colorConfig, DailyQuestTabBundle dailyQuestTabBundle)
        {
            _dailyQuestTabBundle = dailyQuestTabBundle;
            _colorConfig = colorConfig;
        }

        public async UniTask InitAsync(CancellationToken cancellationToken)
        {
            _cancellationToken = cancellationToken;
            _dailyQuestsTabController.Init(_dailyQuestTabBundle);

            DailyQuestModelFactory questModelFactory = new(
                _itemData, _questsData, _localization,
                _quantifiedLocalization, _quantifiedIconLoadingService, _resourceLoadingService);
            _modelFactory = new DailyQuestListModelFactory(_questsData, questModelFactory);

            _timerController = new(_dailyQuestTabBundle, _timeGiver, _questsData.DailyQuestGiver);
            _timerController.OnTimerExpired += () => UpdateQuests().Forget();

            await UpdateQuests();

            await _dailyQuestsTabController.InitChestScrollAndReroll();
            _dailyQuestsTabController.TrySetMasterRewardAsClaimed();
            _dailyQuestsTabController.GoToClicked += OnGoToClicked;
        }

        private void OnGoToClicked(int questId)
        {
            QuestGoToHelper questGoToHelper = new QuestGoToHelper(_questsData, _goToService);
            questGoToHelper.SetFlow(questId, () => UpdateQuests().Forget());
        }
        
        public async void Run()
        {
            _timerController.Update();

            if (!_masterRewardClaimed.IsEmpty())
            {
                UpdateQuests().Forget();
                _dailyQuestsTabController.DailyQuestsPresenterPopupUpdate();
            }

            foreach (int index in _tabRequests)
            {
                SetTabActive(_tabRequests.Get1(index).TabType == SimpleTabType.DailyQuests);
            }

            await UpdateQuestsByRequest();
        }

        private async UniTask UpdateQuests()
        {
            Dictionary<int, DailyQuestsListModel> questsGradedModels = await _modelFactory.CreateQuestsGradedModels(_cancellationToken);

            if (_questsData.DailyQuestGiver.CurrentGrade > _questsData.DailyQuestGiver.MaxGrade)
            {
                _timerController.SetTimerViewState(DailyQuestsTimerController.Position.Lower);
                _dailyQuestsTabController.SetTabViewState(DailyQuestsTabController.ViewState.AllQuestsDone);
                return;
            }

            _timerController.SetTimerViewState(DailyQuestsTimerController.Position.Upper);
            _dailyQuestsTabController.SetTabViewState(DailyQuestsTabController.ViewState.Default);

            await UniTask.WhenAll(
                questsGradedModels.Select(
                    x =>
                        _dailyQuestsTabController.UpdateQuests(x.Key, x.Value)));
        }

        private void SetTabActive(bool on)
        {
            bool tabOpened = _dailyQuestsTabController?.IsActive ?? false;
            if (tabOpened == on)
            {
                return;
            }

            if (on)
            {
                UpdateQuests().Forget();
                _dailyQuestsTabController?.DailyQuestsPresenterPopupUpdate();
                _dailyQuestsTabController?.ShowTab();
            }
            else
            {
                _dailyQuestsTabController.HideTab();
            }
        }

        private async UniTask UpdateQuestsByRequest()
        {
            if (_requestQuestsUpdate.IsEmpty())
            {
                return;
            }

            int rerollQuestId = _requestQuestsUpdate.Get1(0).QuestId;
            _requestQuestsUpdate.GetEntity(0).Destroy();;

            await UpdateQuests();
            if (rerollQuestId > 0)
            {
                _dailyQuestsTabController.HighLightRerollQuest(rerollQuestId);
                _dailyQuestsTabController.DailyQuestsPresenterPopupUpdate();
            }
        }

        public void Destroy()
        {
            _dailyQuestsTabController?.Dispose();
        }
    }
}