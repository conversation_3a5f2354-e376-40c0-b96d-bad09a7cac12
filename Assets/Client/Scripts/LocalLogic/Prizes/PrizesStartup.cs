using System.Threading;
using System.Threading.Tasks;
using Client.Common.Achievements.Generators;
using Client.Common.ECS.LocalWorld;
using Client.Common.ResourcesTopPanel;
using Client.Common.ResourcesTopPanel.Configs;
using Client.Common.ScreenTransitions;
using Client.Common.UI.BackButton;
using Client.Common.UI.Buttons;
using Client.Common.UI.Canvas;
using Client.Common.UI.Rewards.Core;
using Client.Common.UI.Tabs;
using Client.Prizes.Achievements.Systems;
using Client.Prizes.Chests.Quests.Goals;
using Client.Prizes.Chests;
using Client.Prizes.Chests.ChestRoulette;
using Client.Prizes.Chests.Ftue;
using Client.Prizes.Common;
using Client.Prizes.Common.Analytics;
using Client.Prizes.Common.GoTo;
using Client.Prizes.DailyBonus.Tab;
using Client.Prizes.DailyQuests;
using Client.Prizes.DailyQuests.Data;
using Client.Prizes.DailyQuests.Events;
using Client.Prizes.DailyQuests.Systems;
using Client.Prizes.DailyQuests.Systems.Ftue;
using Client.Prizes.DailyQuests.Systems.Reroll;
using Client.Utils.Extensions.EcsExtensions.SystemExtensions;
using Client.Utils.GameLogger;
using UnityEngine;
using VContainer;

namespace Client.Prizes
{
    public struct PrizesScope
    {
    }

    public class PrizesStartup : LocalWorld<PrizesScope>
    {
        [SerializeField] private PrizesLifeTimeScope _lifeTimeScope;
        [SerializeField] private Canvas _mainUiCanvas;
        [SerializeField] private TabsTopView _topTabPanel;
        [SerializeField] private ActionButton _backButton;
        [SerializeField] private DailyQuestsColorConfig _colorConfig;
        [SerializeField] private DailyQuestTabBundle _dailyQuestTabBundle;

        [Inject] private PrizesTabHelper _tabHelper;
        [Inject] private PrizesInitialTabOpener _initialTabOpener;
        [Inject] private RewardPopupContext _popupContext;

        protected override Task OnStart(CancellationToken cancellationToken)
        {
            Systems
                .Add(new TopTabSystem(_topTabPanel, new []
                {
                    new SimpleTab(SimpleTabType.DailyBonus, !_tabHelper.IsDailyBonusTabUnlocked()),
                    new SimpleTab(SimpleTabType.DailyQuests, !_tabHelper.IsDailyQuestTabUnlocked()),
                    new SimpleTab(SimpleTabType.Chests, !_tabHelper.IsChestsTabUnlocked()),
                    new SimpleTab(SimpleTabType.Achievements)
                }))
                .Add(new BackButtonSystem(_backButton))
                .AddFeature(new ResourcesTopPanelFeature(new ResourcesTopPanelConfig(true, true, false, true, true, false)))
                .Add(new HideCanvasSystem(_mainUiCanvas))
                
                .AddFeature(new DailyQuestsFeature(_colorConfig, _dailyQuestTabBundle))
                
                //daily bonus
                .Add(new DailyBonusTabSystem())
                
                //chests
                .Add(new OpenChestQuestProgressSystem())
                .Add(new ChestsTabSystem())
       
                // Achievements
                .Add(new AchievementGenerateSystem())
                .Add(new AchievementsTabSystem(_mainUiCanvas.transform))
                
                //ftue
                .OneFrame<ChangeTabRequest>()
                .Add(new FtueDailyQuestSystem(_dailyQuestTabBundle.PopupsRoot))
                .Add(new FtueChestsSystem())
                
                .Add(new AudioSystem())
                .OneFrame<SwapSound>()
                .OneFrame<ChestRouletteSound>()
                
                .AddFeature(new AnalyticsFeature())
                .OneFrame<ChestRouletteOpenEvent>()
                
                .Add(new MarketGoToSystem())
                .Add(new HttpGoToSystem())
                .Add(new FadeOutSystem())
                .OneFrameEntity<MasterRewardClaimed>()
                .OneFrameEntity<QuestRewardClaimed>()
                .OneFrameEntity<DailyQuestRerolled>()
                .InjectFromContainer(_lifeTimeScope.Container)
                ;

            _initialTabOpener.OpenTab();
            return Task.CompletedTask;
        }
    }
}
