using System.Collections;
using Client.Common.Analytics.Helpers;
using Client.Common.Analytics.Trackers.Ftue;
using Client.Common.BloodProgress.BloodRewards;
using Client.Common.CodeFlow;
using Client.Common.Ftue.Infrastructure;
using Client.Common.Ftue.Infrastructure.Data;
using Client.Common.UI.BackButton;
using Client.Common.UI.Buttons.Abstract;
using Common;
using Leopotam.Ecs;

namespace Client.Profile.ProfileProgress.Systems.Ftue
{
    internal sealed class FtueBloodAltarFirstEnter : FtueSystemBase
    {
        private readonly IAnalyticsHelper _analyticsHelper = default;
        private readonly EcsFilter<CachedBloodDrainData> _cachedInitialData = default;

        private IEnumerator _ftueIt;
        private EcsEntity _buttonBlock;
        private bool _closed;
        
        private readonly IButton _backButton;

        public FtueBloodAltarFirstEnter(IButton backButton)
        {
            _backButton = backButton;
        }

        protected override AccountStep FtueStep => AccountStep.FirstEnterBattleRegion;
        protected override string LogDescription => "profile progress: first enter";

        protected override bool TutorialStartCondition()
        {
            if (IsCompleted(FtueProgressKeys.BloodProgressFirstEnter))
            {
                return false;
            }
            return true;
        }

        protected override void OnInit(CodeFlow<FtueProgress> flow)
        {
            _backButton.Clicked += () => _closed = true;
            
            foreach (int index in _cachedInitialData)
            {
                _cachedInitialData.Get1(index) = new CachedBloodDrainData();
            }

            flow.Entry
                .Then(HideButtons())
                .Then(_analyticsHelper.FireContextualAnalyticsRoutine(FtueSteps.BloodAltarHintSkipped))
                .Then(ShowButtons())
                .Then(_commonHelper.CompleteStageRoutine(FtueProgressKeys.BloodProgressFirstEnter))
                .Then(WaitCloseButtonClicked())
                .Then(_analyticsHelper.FireContextualAnalyticsRoutine(FtueSteps.BloodAltarExit))
                ;
        }

        private IEnumerator HideButtons()
        {
            _buttonBlock = _world.NewEntity();
            _buttonBlock.Get<BackButtonHide>();
            yield break;
        }

        private IEnumerator ShowButtons()
        {
            _buttonBlock.Del<BackButtonHide>();
            yield break;
        }

        private IEnumerator WaitCloseButtonClicked()
        {
            while (!_closed)
            {
                yield return null;
            }
        }
    }
}