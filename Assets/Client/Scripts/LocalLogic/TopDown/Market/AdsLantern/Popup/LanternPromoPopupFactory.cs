using System;
using System.Threading;
using System.Threading.Tasks;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Components;
using Client.Common.Services.Ads.Infrastructure;
using Client.Common.UI.InputLockService;
using Client.Common.UI.Rewards.Core;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.ResourceLoading;
using Client.Utils.StateSwitcher;
using Common;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;

namespace Client.TopDown.Market.AdsLantern.Popup
{
    public class LanternPromoPopupFactory
    {
        public struct PromoPopupConfig
        {
            public ItemQuantity Item;
            public TimeSpan WaitBeforeOpenPopupScreen;
        }

        private readonly LocalEcsWorld _world;
        private readonly ResourceLoadingService _resourceLoadingService;
        private readonly RewardPopupFactory _rewardPopupFactory;
        private readonly MetaNet _metaNet;
        private readonly FullScreenLocker _fullScreenLocker;

        public LanternPromoPopupFactory(
            LocalEcsWorld world,
            RewardPopupFactory rewardPopupFactory,
            FullScreenLocker fullScreenLocker,
            MetaNet metaNet)
        {
            _world = world;
            _fullScreenLocker = fullScreenLocker;
            _rewardPopupFactory = rewardPopupFactory;
            _metaNet = metaNet;
        }

        public async UniTask<bool> Show(PromoPopupConfig config, CancellationToken cancellationToken)
        {
            TaskCompletionSource<bool> showPopupResult = new TaskCompletionSource<bool>();
            cancellationToken.Register(() => showPopupResult.TrySetCanceled());
            _fullScreenLocker.Lock(cancellationToken);

            UniTask<RewardWatchAdsPresenter> popupInit = InitPopup(config, cancellationToken).Preserve();

            UniTask delay = UniTask.Delay(config.WaitBeforeOpenPopupScreen, cancellationToken: cancellationToken);

            await UniTask.WhenAll(popupInit, delay);

            RewardWatchAdsPresenter presenter = popupInit.GetAwaiter().GetResult();
            await presenter.Show(token: cancellationToken);

            _world.NewEntity().Get<AdsLanternOpened>() = new AdsLanternOpened()
            {
                LanternReward = config.Item
            };

            _fullScreenLocker.Unlock(cancellationToken);

            presenter.PopupClosed += result =>
            {
                OnAdsFinished(config.Item, cancellationToken, result);
                showPopupResult.TrySetResult(result != AdsResult.None);
            };

            bool isAdAvailable = presenter.View.WatchAdsButton.CurrentStateId == TheActiveState.Active;
            _world.NewEntity().Get<AdsLanternClosed>() = new AdsLanternClosed()
            {
                LanternReward = config.Item,
                IsAdAvailable = isAdAvailable
            };
            
            return await showPopupResult.Task;
        }

        private async UniTask<RewardWatchAdsPresenter> InitPopup(PromoPopupConfig config, CancellationToken cancellationToken)
        {
            RewardWatchAdsPresenter presenter = await _rewardPopupFactory.Prepare<RewardWatchAdsPresenter, RewardWatchAdsPopup>
                (config.Item, RewardWatchAdsPresenter.ScreenConfig, cancellationToken);
            return presenter;
        }

        private void OnAdsFinished(ItemQuantity item, CancellationToken cancellationToken, AdsResult result)
        {
            if (result == AdsResult.None)
            {
                return;
            }
            
            _world.NewEntity().Get<AdsLanternAdsWatched>() = new AdsLanternAdsWatched()
            {
                LanternReward = item,
                Result = result
            };

            if (result == AdsResult.Finished)
            {
                ItemComponent itemComponent = new()
                {
                    Id = item.Id,
                    Count = item.Count,
                };
                _metaNet.AddItemToInventory(itemComponent, cancellationToken).Forget();
            }
        }
    }
}