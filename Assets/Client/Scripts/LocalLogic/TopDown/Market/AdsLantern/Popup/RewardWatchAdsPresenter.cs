using System;
using Client.Common.Services.Ads.Infrastructure;
using Client.Common.UI.Rewards.Core;

namespace Client.TopDown.Market.AdsLantern.Popup
{
    public class RewardWatchAdsPresenter : RewardPopupPresenter<RewardWatchAdsPopup>
    {
        private const string _ADS_LANTERN_TEXT_ = "market.ads.lantern.text";
        private const string _ADS_LANTERN_TITLE_ = "market.ads.lantern.title";
        private const string _ADS_LANTERN_WATCH_ = "market.ads.lantern.watch";

        public static RewardScreenConfig ScreenConfig => new()
        {
            TitleKey = _ADS_LANTERN_TITLE_,
            RewardDescKey = _ADS_LANTERN_TEXT_
        };

        public Action<AdsResult> PopupClosed;

        protected override void OnOpen()
        {
            base.OnOpen();
            View.WatchAdsButton.SetWatchLimitCount(0);
            View.WatchAdsButton.SetWatchLimit(1);

            string watchButtonText = Localization.Get(_ADS_LANTERN_WATCH_);
            View.WatchAdsButton.SetText(watchButtonText);

            View.WatchAdsButton.AdsFinished += OnAdsFinished;
            View.Close.Clicked += OnCloseClicked;
        }

        private void OnCloseClicked()
        {
            PopupClosed.Invoke(AdsResult.None);
            Hide();
        }

        private void OnAdsFinished(AdsResult x)
        {
            PopupClosed.Invoke(x);
            Hide();
        }

        protected override void OnPreClose()
        {
            View.WatchAdsButton.AdsFinished -= OnAdsFinished;
            View.Close.Clicked -= OnCloseClicked;
            base.OnPreClose();
        }
    }
}