using System.Threading;
using Client.Common.DailyBonus.Data;
using Client.Common.DailyBonus.Popup;
using Client.Common.DailyBonus.Services;
using Client.Common.Network.MetaNet;
using Client.Common.Services.Ads.Infrastructure;
using Client.Common.Services.Ads.Service;
using Client.Common.UI.InputLockService;
using Client.Common.UI.Rewards.Core;
using Client.Common.UI.Rewards.Helpers;
using Client.TopDown.Market.Popups;
using Client.Utils.ResultTool.Results;
using Client.Utils.ViewService;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace Client.TopDown.Market.DailyBonus.Popups
{
    public class DailyBonusPopupSystem : PopupSystemBase
    {
        private readonly PopupViewService _viewService = default;
        private readonly DailyBonusService _dailyBonusService = default;
        private readonly RewardPopupFactory _rewardPopupFactory = default;
        private readonly MetaNet _metaNet = default;
        private readonly FullScreenLocker _screenLocker = default;
        private readonly AdsService _adsService = default;

        private readonly Transform _popupRoot;

        private DailyBonusClaimController _dailyBonusClaimController;
        private DailyBonusPopupPresenter _popupPresenter;
        private UniTaskCompletionSource _rewardClaimedTask;

        protected override string PopupBlockKey => nameof(DailyBonusPopupSystem);
        protected override bool MultiplePopups => true;

        public DailyBonusPopupSystem(Transform popupRoot)
        {
            _popupRoot = popupRoot;
        }

        protected override bool CanShowPopup()
        {
            return _dailyBonusService.IsBonusAvailable() && _adsService.CanOpenRewardedAd();
        }

        protected override async UniTask<Result> TryInitPopup(CancellationToken cancellationToken)
        {
            _dailyBonusClaimController = new DailyBonusClaimController(_dailyBonusService, new AddItemRewardHelper(_metaNet, _rewardPopupFactory, _screenLocker));

            DailyBonusData currentBonusData = _dailyBonusService.GetMultipliedDayBonus(_dailyBonusService.TodayDayOfWeek);
            DailyBonusPopupModel model = new(currentBonusData, _popupRoot);

            _popupPresenter = await _viewService.Prepare<DailyBonusPopupPresenter, DailyBonusPopupView, DailyBonusPopupModel>(model, root: _popupRoot, cancellationToken: cancellationToken);
            return Result.Success();
        }

        protected override UniTask DisplayPopup(CancellationToken cancellationToken)
        {
            _rewardClaimedTask = new UniTaskCompletionSource();

            return UniTask.WhenAll
            (
                OpenPopup(cancellationToken),
                _rewardClaimedTask.Task
            );
        }

        private async UniTask OpenPopup(CancellationToken token)
        {
            _popupPresenter.View.ClaimClicked += ClaimReward;
            _popupPresenter.View.DoubledClaimButton.AdsFinished += ClaimDoubledReward;

            await _popupPresenter.Show(token: token);
        }

        private void ClaimReward()
        {
            UniTask.WhenAll
                   (
                       _dailyBonusClaimController.ClaimReward(CancellationToken),
                       ClosePopup(CancellationToken)
                   )
                   .ContinueWith(OnRewardClaimed);
        }

        private void ClaimDoubledReward(AdsResult AdsResult)
        {
            UniTask.WhenAll
                   (
                       _dailyBonusClaimController.ClaimAdsReward(AdsResult, CancellationToken),
                       ClosePopup(CancellationToken)
                   )
                   .ContinueWith(OnRewardClaimed);
        }

        private async UniTask ClosePopup(CancellationToken cancellationToken)
        {
            await _popupPresenter.Hide(token: cancellationToken);

            _popupPresenter.View.ClaimClicked -= ClaimReward;
            _popupPresenter.View.DoubledClaimButton.AdsFinished -= ClaimDoubledReward;
            _popupPresenter = null;
        }

        private void OnRewardClaimed()
        {
            _rewardClaimedTask.TrySetResult();
        }
    }
}