using System.Collections;
using Client.Common.Analytics.Helpers;
using Client.Common.Analytics.Trackers.Ftue;
using Client.Common.BloodColba;
using Client.Common.BlurTool.ScreenshotBlur;
using Client.Common.CodeFlow;
using Client.Common.Ftue.Infrastructure;
using Client.Common.Ftue.Infrastructure.Data;
using Client.Common.Player.Controllers;
using Client.Common.UI.Focuser;
using Client.TopDown.Region.Systems.Ftue;
using Client.Utils.Visible;
using Common;
using Leopotam.Ecs.Ui.Systems;
using UnityEngine;

namespace Client.TopDown.Market.Systems.Ftue
{
    public class FtueBloodAltarSystem : FtueSystemBase
    {
        private readonly PlayerManager _playerManager = default;
        private readonly ScreenshotBlurService _blur = default;
        private readonly RegionFtueHelper _regionFtue = default;
        private readonly InputsFtueHelper _inputsFtue = default;
        private readonly IAnalyticsHelper _analyticsHelper = default;

        private readonly Vector3 _altarPoint = new(9f,0.05f,12f);

        private readonly VisibilityController _hud;
        private UiFocuser _uiFocuser;

        [EcsUiNamed(BloodColbaHelper.BLOOD_COLBA_ICON_KEY)]
        private BloodColbaView _bloodColbaIconView;

        private readonly RectTransform _rootTransform;

        public FtueBloodAltarSystem(RectTransform rootTransform, VisibilityController hud)
        {
            _rootTransform = rootTransform;
            _hud = hud;
        }

        protected override AccountStep FtueStep => AccountStep.FirstEnterBattleRegion;
        protected override string LogDescription => "market: blood auto drain";
        protected override bool TutorialStartCondition()
        {
            if (IsCompleted(FtueProgressKeys.BloodProgressFirstEnter))
            {
                return false;
            }
            return true;
        }

        protected override void OnInit(CodeFlow<FtueProgress> flow)
        {
            _uiFocuser = new UiFocuser(_blur);
            SyncBloodCurrency();
            _hud.Hide();

            flow.Entry
                .Then(_analyticsHelper.FireAnalyticsRoutine(FtueSteps.EnterMarket))
                .Then(_inputsFtue.SetUserInputActive(false))
                .Then(HighlightColba())
                .Then(_regionFtue.MoveUserTo(_altarPoint, 0.2f))
                .Then(_analyticsHelper.FireContextualAnalyticsRoutine(FtueSteps.DrainedBlood))
                .Then(_commonHelper.BlockOtherTutorials());
        }

        private void SyncBloodCurrency()
        {
            int bloodCurrency = _playerManager.Session.Inventory.BloodBalance.Amount;
            _playerManager.Session.BloodColba.Blood = (uint)bloodCurrency;
        }

        private IEnumerator HighlightColba()
        {
            const float showTime = 2f;

            _bloodColbaIconView.SetGlowActive(true);
            _uiFocuser.Focus(_bloodColbaIconView.GetComponent<RectTransform>(), _rootTransform);
            ShowBloodColba();

            float time = Time.time + showTime;
            while (time > Time.time)
            {
                yield return null;
            }

            _uiFocuser.HideBackground();
            _bloodColbaIconView.SetGlowActive(false);

            _bloodColbaIconView.Highlight();
        }

        private void ShowBloodColba()
        {
            BloodColbaHelper bloodColbaHelper = new(_playerManager.Session);
            BloodColbaAnimation bloodColbaAnimation = new();
            int bloodColbaStateId = bloodColbaHelper.CalculateCurrentBloodColbaState();
            bloodColbaAnimation.PlayIdle(_bloodColbaIconView, bloodColbaStateId, _playerManager.Session.BloodColba.Blood.ToString());
        }
    }
}