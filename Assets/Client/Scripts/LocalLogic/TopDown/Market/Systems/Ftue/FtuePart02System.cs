using Client.Common.Analytics.Helpers;
using Client.Common.Analytics.Trackers.Ftue;
using Client.Common.CodeFlow;
using Client.Common.Ftue.Infrastructure;
using Client.Common.Ftue.Infrastructure.Data;
using Client.Common.Npc;
using Client.TopDown.Common.SpeechTooltip.Controllers;
using Client.TopDown.Cutscene.Controllers;
using Client.TopDown.Region.Systems.Ftue;
using Client.Utils.Visible;
using Common;
using SpeechIdents = Client.TopDown.Market.Data.SpeechIdents;

namespace Client.TopDown.Market.Systems.Ftue
{
    internal sealed class FtuePart02System : FtueSystemBase
    {
        private const string _CUTSCENE_KEY_ = "BlacksmithUnlock";

        private readonly IAnalyticsHelper _analyticsHelper = default;
        private readonly RegionFtueHelper _regionHelper = default;
        private readonly SpeechHelper _speechHelper = default;
        private readonly NpcFtueHelper _npcHelper = default;
        private readonly CutsceneController _cutsceneController = default;

        private readonly VisibilityController _hud;

        public FtuePart02System(VisibilityController hud)
        {
            _hud = hud;
        }

        protected override AccountStep FtueStep => AccountStep.FirstEnterBattleRegion;
        protected override string LogDescription => "market: target to blacksmith";

        protected override bool TutorialStartCondition()
        {
            if (IsCompleted(FtueProgressKeys.BlacksmithUnlock))
            {
                return false;
            }
            return true;
        }

        protected override void OnInit(CodeFlow<FtueProgress> flow)
        {
            _hud.Hide();
            flow.Entry
                .Then(_regionHelper.BlockExit(SpeechIdents.ExitBlocks.NPC_EXIT_BLOCK_KEY))
                .Then(_npcHelper.EnableNpc(NpcType.Blacksmith))
                .Then(_npcHelper.AttractNpc(NpcType.Blacksmith))
                .Then(_cutsceneController.Cutscene(NpcType.Blacksmith, _CUTSCENE_KEY_, SpeechIdents.NpcSpeechPhrases.BLACKSMITH_FTUE_PHRASE, 5f))
                .Then(_npcHelper.SetTargetToNpcRoutine(NpcType.Blacksmith))
                .Then(_speechHelper.ShowHintIfUserIsIdle(3.5f))
                .Then(_regionHelper.WaitForUserArrival(_npcHelper.FindNpcPosition(NpcType.Blacksmith)))
                .Then(_analyticsHelper.FireContextualAnalyticsRoutine(FtueSteps.ReachBlacksmith))
                .Then(_commonHelper.BlockOtherTutorials())
                ;
        }
    }
}