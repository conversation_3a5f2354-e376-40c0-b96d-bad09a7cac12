using System.Collections;
using Client.Common.Analytics.Helpers;
using Client.Common.Analytics.Trackers.Ftue;
using Client.Common.CodeFlow;
using Client.Common.DailyBonus.Services;
using Client.Common.Ftue.Helpers;
using Client.Common.Ftue.Infrastructure;
using Client.Common.TimeGiver.Abstractions;
using Client.TopDown.Market.UI;
using Common;

namespace Client.TopDown.Market.Systems.Ftue.Prizes
{
    public class FtueDailyBonusUnlockSystem : FtueSystemBase
    {
        private readonly CommonFtueHelper _commonFtueHelper;
        private readonly DailyBonusService _dailyBonusService = default;
        private readonly ITimeGiver _timeGiver = default;
        private readonly IAnalyticsHelper _analyticsHelper = default;

        private readonly PrizeButtonView _prizeButton;

        public FtueDailyBonusUnlockSystem(PrizeButtonView prizeButton)
        {
            _prizeButton = prizeButton;
        }

        protected override AccountStep FtueStep => AccountStep.DailyBonusUnlocked;
        protected override string LogDescription => "market: daily bonus unlock";

        protected override bool TutorialStartCondition() => true;

        protected override void OnInit(CodeFlow<FtueProgress> flow)
        {
            flow.Entry
                .Then(UnlockDailyBonuses())
                .Then(_analyticsHelper.FireContextualAnalyticsRoutine(FtueSteps.DailyBonusStarted))
                .Then(_commonHelper.CompleteStageRoutine(AccountStep.DailyBonusUnlocked));
        }

        private IEnumerator UnlockDailyBonuses()
        {
            _dailyBonusService.NextBonusTime = _timeGiver.UtcNow;
            DisplayPrizesIcon();

            yield break;
        }

        private void DisplayPrizesIcon()
        {
            _prizeButton.Root.SetActive(true);
            _prizeButton.SetNotificationStatus(true);
        }
    }
}