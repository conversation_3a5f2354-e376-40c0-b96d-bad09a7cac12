namespace Client.TopDown.Region.Data
{
    public static class LocalIdents
    {
        public static class AI
        {
            public const float DECISION_HOOK_RANGE = 4f * 0.9f;
        }
        public static class Paths
        {
            public const string FTUE_HOOK = "Images/UI/Region/FtueHook";
            public const string FTUE_BOMB = "Images/UI/Region/FtueBomb";
            public const string FTUE_BLOOD_COLBA = "Images/UI/Region/FtueBloodColba";
        }
        
        public static class Localization
        {
            public const string ARENA_ENEMY1 = "ftue.region.speech.1";
            public const string BOMBER_SPEECH1 = "ftue.region.speech.3";
            public const string BOMBER_SPEECH2 = "ftue.region.speech.4";
            public const string ENEMY_SPEECH1 = "ftue.region.speech.5";
            public const string ENEMY_SMOKE1 = "ftue.region.speech.bot.smoke.1";
            public const string ENEMY_SMOKE2 = "ftue.region.speech.bot.smoke.2";
            public const string USER_LOOT_HINT = "ftue.region.speech.user.loot";
        }
        
        public static class UI
        {
            public const string HOOK_BTN = "Hook";
            public const string BOMB_BTN = "Bomb";
            public const string JOYSTICK_ZONE = "JoystickZone";
            public const string JOYSTICK_HANDLE = "JoystickHandle";
            public const string JOYSTICK_BASE = "JoystickBase";

            public const string SAFE_ZONE = "MainCanvasSafeZone";
            public const string DIALOGS_ROOT = "RootForDialogs";
        }

        public static class Keys
        {
            public const string REGION_MOVEMENT_SPEED = "region.movementspeed";
        }
        
        public static class Mmr
        {
            public const int WIN_RATE_BORDER_PERCENT_GRADE0 = 50;
            public const int WIN_RATE_BORDER_PERCENT_GRADE1 = 60;
            public const int WIN_RATE_BORDER_PERCENT_GRADE2 = 70;
            public const int WIN_RATE_BORDER_PERCENT_GRADE3 = 71;
            public const int BORDER_FIGHTS_MMR = 15;
        }
    }
}