using Client.Common.CSV;

namespace Client.TopDown.Region.Helpers
{
    public enum UnitType
    {
        Player,
        Bot
    }

    public enum HookParry
    {
        Front,
        Side,
        Back
    }

    public class HookParryChanceHelper
    {
        private const string _HOOK_PARRY_LOW_BORDER_ = "hook.parry.Ftue.rankBorder";
        private const string _HOOK_PARRY_TOP_BORDER_ = "hook.parry.topRanks.rankBorder";
        private const string _HOOK_PARRY_KEY_FORMAT_ = "hook.parryChance.{0}.{1}.tier{2}";

        private readonly GoogleDocsData _googleDocsData;
        private readonly int _rankLowBorder;
        private readonly int _rankTopBorder;

        public HookParryChanceHelper(GoogleDocsData googleDocsData)
        {
            _googleDocsData = googleDocsData;
            _rankLowBorder = googleDocsData.GetIntNumber(_HOOK_PARRY_LOW_BORDER_);
            _rankTopBorder = googleDocsData.GetIntNumber(_HOOK_PARRY_TOP_BORDER_);
        }

        public float GetChance(
            UnitType unitType,
            HookParry side,
            int rank)
        {
            int tier;

            if (rank <= _rankLowBorder)
            {
                tier = 1;
            }
            else if (rank <= _rankTopBorder)
            {
                tier = 2;
            }
            else
            {
                tier = 3;
            }

            return _googleDocsData.GetNumber(
                string.Format(
                    _HOOK_PARRY_KEY_FORMAT_,
                    unitType.ToString().ToLowerInvariant(),
                    side.ToString().ToLowerInvariant(),
                    tier.ToString()), 1);
        }
    }
}