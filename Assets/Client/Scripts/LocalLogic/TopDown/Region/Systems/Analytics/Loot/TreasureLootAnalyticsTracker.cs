using System.Collections.Generic;
using System.Linq;
using Client.Common.Analytics.Infrastructure.Events;
using Client.Common.LocalLogic.TopDown.Treasure;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.TopDown.Region.Looting.Components.Unit;
using Client.Utils.ECS.LocalWorld;
using Leopotam.Ecs;
using ItemQuantity = Client.Common.Network.MetaNet.ItemQuantity;

namespace Client.TopDown.Region.Systems.Analytics.Loot
{
    public class TreasureLootAnalyticsTracker : LootSourceSearchAnalyticsTracker
    {
        private const string _TREASURE_SEARCH_START_ = "treasure_search_start";
        private const string _TREASURE_SEARCH_COMPLETE_ = "treasure_search_complete";

        public TreasureLootAnalyticsTracker(LocalEcsWorld world, PlayerManager playerManager) : base(world, playerManager)
        {
        }

        protected override void FireStart()
        {
            FireStartEvent(_TREASURE_SEARCH_START_);
        }

        protected override void FireLootSuccess(EcsEntity lootEntity, ref UnitLootRewardClaimed rewardClaimed)
        {
            ref Treasure treasure = ref lootEntity.Get<Treasure>();

            IEnumerable<ItemQuantity> rewards = GetGroupedRewardsById(rewardClaimed.Rewards);

            foreach (ItemQuantity reward in rewards)
            {
                FireCompleteEvent(_TREASURE_SEARCH_COMPLETE_, treasure.Guid, true, 
                                  reward.Id, (int)reward.Count, GetItemBalance(reward));
            }
        }

        protected override void FireFailure(EcsEntity lootEntity)
        {
            ref Treasure treasure = ref lootEntity.Get<Treasure>();
            FireCompleteEvent(_TREASURE_SEARCH_COMPLETE_, treasure.Guid, false);
        }

        protected override bool IsLootSourceValid(EcsEntity lootEntity) => lootEntity.Has<Treasure>();

        private void FireStartEvent(in string eventName)
        {
            EcsEntity analyticsEvent = World.NewEntity();

            analyticsEvent.Get<AnalyticsCustomEvent>().Id = eventName;

            analyticsEvent.Get<AnalyticsParameters>().Parameters = new Dictionary<string, string>
                                                                   {
                                                                       ["Action"] = "Action"
                                                                   };
        }

        private void FireCompleteEvent(string eventName, string lootSourceGuid, bool isSuccessful, 
                                       int? rewardId = null, int count = 0, int balance = 0)
        {
            EcsEntity analyticsEvent = World.NewEntity();

            analyticsEvent.Get<AnalyticsCustomEvent>().Id = eventName;

            string rewardIdValue = rewardId.HasValue ? rewardId.Value.ToString() : EMPTY_REWARD_ID;

            analyticsEvent.Get<AnalyticsParameters>().Parameters = new Dictionary<string, string>
            {
                ["Name"] = $"{lootSourceGuid}",
                ["State"] = $"{isSuccessful}",
                ["ItemID"] = $"{rewardIdValue}",
                ["Count"] = $"{count}",
                ["Balances"] = $"{balance}",
            };
        }
    }
}