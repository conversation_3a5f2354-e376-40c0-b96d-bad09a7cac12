using System;
using System.Threading;
using Client.Common.BloodColba;
using Client.Common.BloodColba.Events;
using Client.Common.Components.Unit;
using Client.Common.LocalLogic.TopDown.Unit;
using Client.Common.Player.Controllers;
using Client.TopDown.Region.Systems.Helpers;
using Client.Utils.CustomTweens.VFXService;
using Client.Utils.CustomTweens.VFXService.TweenSequence;
using Client.Utils.ECS.ECSAsyncInitSystem;
using Client.Utils.ResourceLoading;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using UnityEngine;
using Object = UnityEngine.Object;

namespace Client.TopDown.Region.Systems
{
    public class BloodColbaMaxSizeAnimationSystem : IEcsAsyncInitSystem, IEcsRunSystem, IEcsDestroySystem
    {
        private const int _BLOOD_DROP_START_DELAY_ = 2;
        private const int _BLOOD_DROP_LENGTH_ = 7;

        private readonly ResourceLoadingService _resourceLoadingService = default;

        private readonly EcsFilter<TopDownUnit, PlayerFlag> _users = default;
        private readonly EcsFilter<BloodColbaMaxSizeChangeEvent> _bloodColbaMaxSizeChangeEvent = default;

        private readonly BloodColbaView _bloodColbaView;
        private readonly BloodColbaAnimation _bloodColbaAnimation;
        private readonly TweenSequence _tweenSequence;

        private BloodColbaDropBloodFactory _bloodColbaDropBloodFactory;
        private CancellationTokenSource _createViewCts;

        public BloodColbaMaxSizeAnimationSystem(BloodColbaView bloodColbaView)
        {
            _bloodColbaView = bloodColbaView;
            _bloodColbaAnimation = new BloodColbaAnimation();
            _tweenSequence = new TweenSequence(new TweenManager());
        }

        public async UniTask InitAsync(CancellationToken cancellationToken)
        {
            _bloodColbaDropBloodFactory = new BloodColbaDropBloodFactory(_resourceLoadingService);
        }

        public async void Run()
        {
            if (_bloodColbaMaxSizeChangeEvent.IsEmpty())
            {
                return;
            }

            BloodColbaMaxSizeChangeEvent bloodColbaMaxSizeChangeEvent = _bloodColbaMaxSizeChangeEvent.Get1(0);
            int oldBloodInColba = bloodColbaMaxSizeChangeEvent.OldBloodInColba;
            if (oldBloodInColba <= bloodColbaMaxSizeChangeEvent.NewBloodInColba)
            {
                return;
            }

            StartDropBloodAnimations(bloodColbaMaxSizeChangeEvent.NewBloodInColba);
            await GetDropGroundBlood();
        }

        private void StartDropBloodAnimations(int bloodColbaMaxSize)
        {
            _bloodColbaView.BloodColbaIcon.SetActive(true);
            _bloodColbaAnimation.PlayDropSize(_bloodColbaView, bloodColbaMaxSize.ToString());
        }

        private async UniTask GetDropGroundBlood()
        {
            _createViewCts?.Cancel();
            _createViewCts = new CancellationTokenSource();

            Transform transform = _users.Get1(0).Visual.transform;
            GameObject gameObject = await _bloodColbaDropBloodFactory.GetDropBloodVFX(transform, _createViewCts.Token);
            _tweenSequence?.InstantComplete(true);
            _tweenSequence?.AppendDelay(TimeSpan.FromSeconds(_BLOOD_DROP_START_DELAY_))
                          .Append(SetBloodObject)
                          .AppendDelay(TimeSpan.FromSeconds(_BLOOD_DROP_LENGTH_))
                          .Append(DestroyBloodObject).Play();
            return;

            void SetBloodObject()
            {
                gameObject.SetActive(true);
            }
            
            void DestroyBloodObject()
            {
                Object.Destroy(gameObject);
            }
        }

        public void Destroy()
        {
            _tweenSequence?.Dispose();
        }
    }
}