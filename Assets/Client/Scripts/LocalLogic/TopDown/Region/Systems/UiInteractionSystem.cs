using Client.Common.Ftue.Helpers;
using Client.Common.Ftue.Infrastructure;
using Client.TopDown.Common.Timers;
using Client.TopDown.Region.Services;
using Client.TopDown.Region.UnityComponents;
using Leopotam.Ecs;
using Leopotam.Ecs.Ui.Components;
using UnityEngine;

namespace Client.TopDown.Region.Systems
{
    internal sealed class UiInteractionSystem : IEcsRunSystem
    {
        private const string _USER_TIMER_UI_ = "UserTimer";

        private const int _INVALID_TOUCH_ = -1000;
        private const float _DRAG_THRESHOLD_ = 10f / 750f;

        // auto-injected fields.
        private readonly LocalSharedState _lss = default;
        private readonly FtueProgress _ftueProgress = default;
        private readonly EcsFilter<EcsUiDownEvent> _presses = default;
        private readonly EcsFilter<EcsUiUpEvent> _releases = default;

        private int _touchId = _INVALID_TOUCH_;
        private Vector2 _touchPoint;

        private bool _exitHintShowing;

        public void Run()
        {
            if (_lss.UiLocked)
            {
                return;
            }

            if (_touchId == _INVALID_TOUCH_)
            {
                foreach (int idx in _presses)
                {
                    ref EcsUiDownEvent evt = ref _presses.Get1(idx);
                    _touchId = evt.PointerId;
                    _touchPoint = evt.Position;
                }
            }

            foreach (int idx in _releases)
            {
                ref EcsUiUpEvent e = ref _releases.Get1(idx);
                if (_touchId == e.PointerId)
                {
                    _touchId = _INVALID_TOUCH_;
                    // e.Sender.PlayUi (UiAnimationType.Enabled);
                    var dist = (e.Position - _touchPoint).magnitude / Screen.width;
                    if (dist > _DRAG_THRESHOLD_)
                    {
                        continue;
                    }

                    switch (e.WidgetName)
                    {
                        case _USER_TIMER_UI_:
                            OnUserTimerPressed(e);
                            break;
                    }
                }
            }
        }

        private void OnUserTimerPressed(EcsUiUpEvent e)
        {
            if (!_ftueProgress.Conditions.IsTraumaCalculationEnabled())
            {
                return;
            }
            UserTimerView view = e.Sender.GetComponent<UserTimerView>();
            view.Show(true);
        }
    }
}