using Client.TopDown.Region.Components;
using Client.TopDown.SpawnSystem.Components;
using Client.TopDown.SpawnSystem.Creators;
using Client.Utils.ECS.LocalWorld;
using Leopotam.Ecs;

namespace Client.TopDown.SpawnSystem.Systems
{
    public class BotSpawnCreatorSystem : IEcsRunSystem
    {
        private readonly LocalEcsWorld _world = default;
        private readonly SpawnCreator _spawnCreator = default;

        private readonly EcsFilter<UnitSpawnRequest, SpawnRequestVerified> _spawnRequests = default;

        public void Run()
        {
            CreateSpawners();
        }

        private void CreateSpawners()
        {
            if (_spawnRequests.IsEmpty())
            {
                return;
            }

            // Each spawn in separate frame
            EcsEntity spawnRequest = _spawnRequests.GetEntity(0);
            CreateSpawn(spawnRequest);
            spawnRequest.Destroy();
        }

        private void CreateSpawn(EcsEntity entity)
        {
            UnitSpawnRequest spawnRequest = entity.Get<UnitSpawnRequest>();
            
            if (_spawnCreator.TryGetSpawn(
                    entity.Get<RankGeneratorComponent>().RankGenerator,
                    entity.Get<TryPointProviderComponent>().PointProvider,
                    entity.Get<ShurikenGeneratorComponent>().ShurikenGenerator,
                    entity.Get<AmuletGeneratorComponent>().AmuletGenerator,
                    entity.Get<PowerStoneGeneratorComponent>().PowerStoneGenerator,
                    entity.Get<BombPocketGeneratorComponent>().BombPocketGenerator,
                    entity.Get<HookPocketGeneratorComponent>().HookPocketGenerator,
                    entity.Get<SkinGeneratorComponent>().SkinGenerator,
                    entity.Get<TraumaGeneratorComponent>().TraumaGenerator,
                    out UnitSpawn unitSpawn))
            {
                EcsEntity spawnEntity = _world.NewEntity();
                spawnEntity.Get<UnitSpawn>() = unitSpawn;
                spawnEntity.Get<AiSpawnData>().Construct(spawnRequest.RegionType);
            }
        }
    }
}