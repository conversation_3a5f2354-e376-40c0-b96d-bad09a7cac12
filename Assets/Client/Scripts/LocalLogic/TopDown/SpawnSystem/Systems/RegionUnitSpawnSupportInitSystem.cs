using Client.Common.CSV;
using Client.Common.Player.Controllers;
using Client.TopDown.Region.Services;
using Client.Utils.Extensions;
using Leopotam.Ecs;
using UnityEngine;
using Random = UnityEngine.Random;

namespace Client.TopDown.SpawnSystem.Systems
{
    internal class RegionUnitSpawnSupportInitSystem : IEcsPreInitSystem, IEcsInitSystem
    {
        private readonly PlayerManager _playerManager = default;
        private readonly GoogleDocsData _googleDocsData = default;

        private readonly RegionUnitSpawnSupport _regionUnitSpawnSupport;
        
        private int _negativeBotDelta;
        private int _positiveBotDelta;
        private int _roomCapacity;
        private float _chanceToKeepNumberFromMap;
        
        private int BotDelta => Random.Range(_negativeBotDelta, _positiveBotDelta + 1);

        private RegionSessionData RegionSessionData => _playerManager.Session.RegionSessionData;
        
        private const string _MAX_ROOM_CAPACITY_KEY_ = "room.search.bot.count.max";
        private const string _NEGATIVE_BOT_DELTA_KEY_ = "region.bot.spawn.startCount.delta.negative";
        private const string _POSITIVE_BOT_DELTA_KEY_ = "region.bot.spawn.startCount.delta.positive";
        private const string _IGNORE_DELTA_FROM_MAP_ = "region.bot.spawn.startCount.plusOne.chance";

        public RegionUnitSpawnSupportInitSystem(RegionUnitSpawnSupport regionUnitSpawnSupport)
        {
            _regionUnitSpawnSupport = regionUnitSpawnSupport;
        }

        public void PreInit()
        {
            _negativeBotDelta = (int) _googleDocsData.GetNumber(_NEGATIVE_BOT_DELTA_KEY_);
            _positiveBotDelta = (int) _googleDocsData.GetNumber(_POSITIVE_BOT_DELTA_KEY_);
            _roomCapacity = (int) _googleDocsData.GetNumber(_MAX_ROOM_CAPACITY_KEY_);
            _chanceToKeepNumberFromMap = _googleDocsData.GetNumber(_IGNORE_DELTA_FROM_MAP_);
        }
        
        public void Init()
        {
            int startUnitCount = CalculateStartUnitCount();
            _regionUnitSpawnSupport.Init(startUnitCount - 1, _roomCapacity);
        }

        private int CalculateStartUnitCount()
        {
            int unitDelta = 1;
            int startBotCount = IsExitFromBattle() ? (int)RegionSessionData.BotData.BotCountOnEnterBattle : (int)RegionSessionData.BotData.StartBotCount;
            if (IsExitFromBattle() || !RandomX.IsLucky(_chanceToKeepNumberFromMap))
            {
                unitDelta = BotDelta;
            }

            int count = startBotCount + unitDelta;
            int clampedCount = Mathf.Clamp(count, 1, _roomCapacity);
            RegionSessionData.BotData.StartBotCount = (uint)clampedCount;

            return clampedCount;

            bool IsExitFromBattle() => !RegionSessionData.EnteredFromMap;
        }
    }
}