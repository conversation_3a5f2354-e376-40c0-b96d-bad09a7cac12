using Client.Common.AI.Data;
using Client.Common.AI.Givers;
using Client.Common.CSV;
using Client.Common.LocalLogic.TopDown.Unit;
using Client.TopDown.Region.Components;
using Client.TopDown.Region.Components.AI;
using Client.TopDown.Region.Services;
using Client.Utils.ECS.LocalWorld;
using Leopotam.Ecs;
using UnityEngine;
using Random = UnityEngine.Random;

namespace Client.TopDown.SpawnSystem.Systems.SpawnRequestCreators
{
    internal class SpawnRequestCreatorByTimerSystem : IEcsPreInitSystem, IEcsInitSystem, IEcsRunSystem
    {
        private readonly LocalEcsWorld _world = default;
        private readonly BotAITypeGiver _botAITypeGiver = default;
        private readonly GoogleDocsData _googleDocsData = default;

        private readonly EcsFilter<TopDownUnit, Ai> _bots = default;

        private readonly RegionUnitSpawnSupport _regionUnitSpawnSupport;

        private float _minSpawnCooldown;
        private float _maxSpawnCooldown;
        private float _spawnTimer;

        private float SpawnCooldown => Random.Range(_minSpawnCooldown, _maxSpawnCooldown);

        private const string _MIN_SPAWN_COOLDOWN_KEY_ = "region.bot.spawn.cooldown.min";
        private const string _MAX_SPAWN_COOLDOWN_KEY_ = "region.bot.spawn.cooldown.max";

        public SpawnRequestCreatorByTimerSystem(RegionUnitSpawnSupport regionUnitSpawnSupport)
        {
            _regionUnitSpawnSupport = regionUnitSpawnSupport;
        }

        public void PreInit()
        {
            _minSpawnCooldown = _googleDocsData.GetNumber(_MIN_SPAWN_COOLDOWN_KEY_);
            _maxSpawnCooldown = _googleDocsData.GetNumber(_MAX_SPAWN_COOLDOWN_KEY_);
            _spawnTimer = SpawnCooldown;
        }

        public void Init()
        {
            for (int i = 0; i < _regionUnitSpawnSupport.StartBotsCount; i++)
            {
                CreateSpawnRequest();
            }
        }

        public void Run()
        {
            if (IsRoomFull())
            {
                return;
            }
            
            if (_spawnTimer <= 0)
            {
                CreateSpawnRequest();
                _spawnTimer = SpawnCooldown;
            }

            _spawnTimer -= Time.deltaTime;
        }

        private void CreateSpawnRequest()
        {
            RegionAIType aiType = _botAITypeGiver.GetRegionAIType();
            
            if (aiType == RegionAIType.Ninja)
            {
                return;
            }

            _world.NewEntity().Get<UnitSpawnRequest>().Construct(aiType);
        }
        
        private bool IsRoomFull()
        {
            return _bots.GetEntitiesCount() >= _regionUnitSpawnSupport.MaxUnitsCount - 1;
        }
    }
}