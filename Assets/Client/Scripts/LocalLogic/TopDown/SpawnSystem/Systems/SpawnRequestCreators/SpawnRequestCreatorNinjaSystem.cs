using Client.Common.AI.Data;
using Client.Common.AI.Givers;
using Client.Common.Components.Unit;
using Client.Common.LocalLogic.TopDown.Unit;
using Client.TopDown.Common.Extensions;
using Client.TopDown.Region.Components;
using Client.TopDown.Region.Components.AI;
using Client.TopDown.Region.NinjaHidingSpot.Components;
using Client.TopDown.Region.Services;
using Client.Utils.ECS.LocalWorld;
using Leopotam.Ecs;
using UnityEngine;

namespace Client.TopDown.SpawnSystem.Systems.SpawnRequestCreators
{
    internal class SpawnRequestCreatorNinjaSystem : IEcsRunSystem
    {
        private const float _MIN_NINJA_RADIUS_ = 3f;
        
        private readonly LocalEcsWorld _world = default;
        private readonly BotAITypeGiver _botAITypeGiver = default;
        
        private readonly RegionUnitSpawnSupport _regionUnitSpawnSupport;
        
        private readonly EcsFilter<TopDownUnit, Ai> _bots = default;
        private readonly EcsFilter<NinjaHidingSpotComponent>.Exclude<NinjaHidingSpotTimerComponent> _hidingSpots = default;
        private readonly EcsFilter<NinjaHidingSpotComponent, NinjaHidingSpotTimerComponent> _hidingSpotsTimers = default;
        private readonly EcsFilter<TopDownUnit, PlayerFlag> _playerUnits = default;

        public SpawnRequestCreatorNinjaSystem(RegionUnitSpawnSupport regionUnitSpawnSupport)
        {
            _regionUnitSpawnSupport = regionUnitSpawnSupport;
        }

        public void Run()
        {
            foreach (int unitIdx in _playerUnits)
            {
                CreateNinja(unitIdx);
                RemoveNinja();
            }
        }

        private void CreateNinja(int unitIdx)
        {
            ref TopDownUnit unit = ref _playerUnits.Get1(unitIdx);
            foreach (int idx in _hidingSpots)
            {
                ref EcsEntity ninjaSpotEntity = ref _hidingSpots.GetEntity(idx);
                ref NinjaHidingSpotComponent hidingSpotComponent = ref _hidingSpots.Get1(idx);
                
                if (!hidingSpotComponent.IsAvailable)
                {
                    continue;
                }
                
                if (hidingSpotComponent.IsEntitySet && !hidingSpotComponent.CreatedEntity.IsAlive())
                {
                    ResetSpot(ref hidingSpotComponent);
                }

                float distance = DistanceXZ(unit.Position, hidingSpotComponent.Position);
                if (distance >= _MIN_NINJA_RADIUS_ && distance <= hidingSpotComponent.CreationDistance)
                {
                    SpawnNinja(ref hidingSpotComponent, ninjaSpotEntity);
                }
            }
        }

        private void SpawnNinja(ref NinjaHidingSpotComponent hidingSpotComponent, EcsEntity ninjaSpotEntity)
        {
            if (_botAITypeGiver.GetRegionAIType() != RegionAIType.Ninja ||
                _bots.GetEntitiesCount() >= _regionUnitSpawnSupport.MaxUnitsCount - 1)
            {
                return;
            }
            
            RequestSpawnNinja(hidingSpotComponent);
            hidingSpotComponent.ActivationTime = Time.time;
            ninjaSpotEntity.Get<NinjaHidingSpotTimerComponent>();
        }

        private void RemoveNinja()
        {
            foreach (int idx in _hidingSpotsTimers)
            {
                ref EcsEntity ninjaSpotEntity = ref _hidingSpotsTimers.GetEntity(idx);
                ref NinjaHidingSpotComponent hidingSpotComponent = ref _hidingSpotsTimers.Get1(idx);

                // TODO: Destroying a non active NinjaUnit is not the responsibility of NinjaSpot
                if (Time.time - hidingSpotComponent.ActivationTime > hidingSpotComponent.DeActivationTime)
                {
                    RemoveSpawnNinja(ref hidingSpotComponent);
                    ninjaSpotEntity.Del<NinjaHidingSpotTimerComponent>();
                }
            }
        }

        private float DistanceXZ(Vector3 first, Vector3 second)
        {
            Vector3 distanceXZ = second - first;
            distanceXZ.y = 0;
            return distanceXZ.magnitude;
        }

        private void RequestSpawnNinja(NinjaHidingSpotComponent ninjaHidingSpotComponent)
        {
            EcsEntity unitSpawnEntity = _world.NewEntity();
            unitSpawnEntity.Get<UnitSpawnRequest>().Construct(RegionAIType.Ninja);
            unitSpawnEntity.Get<NinjaHidingSpotComponent>() = ninjaHidingSpotComponent;
        }

        private void RemoveSpawnNinja(ref NinjaHidingSpotComponent hidingSpotComponent)
        {
            if (hidingSpotComponent.IsEntitySet)
            {
                if (!hidingSpotComponent.CreatedEntity.IsAlive())
                {
                    ResetSpot(ref hidingSpotComponent);
                    return;
                }
                
                TopDownUnit unit = hidingSpotComponent.CreatedEntity.Get<TopDownUnit>();  
                if (!unit.IsInteracting() && !unit.IsLooting())
                {
                    hidingSpotComponent.CreatedEntity.Get<UnitRemove>().Timestamp = -1;
                    ResetSpot(ref hidingSpotComponent);
                }
            }
        }

        private void ResetSpot(ref NinjaHidingSpotComponent hidingSpotComponent)
        {
            hidingSpotComponent.CreatedEntity = default;
            hidingSpotComponent.IsEntitySet = false;
        }
    }
}