using System;

namespace Client.Utils.FireBase
{
    public class CrashlyticsException : Exception, IEquatable<CrashlyticsException>
    {
        public CrashlyticsException(string e) : base($"[Log exception] {e}")
        {
        }

        public static bool operator ==(CrashlyticsException e1, CrashlyticsException e2)
        {
            return e1 != null && e2 != null && e1.StackTrace == e2.StackTrace && e1.Message == e2.Message;
        }

        public static bool operator !=(CrashlyticsException e1, CrashlyticsException e2)
        {
            return !(e1 == e2);
        }

        public bool Equals(CrashlyticsException other) => this == other;

        public override int GetHashCode()
        {
            return HashCode.Combine(Message, StackTrace);
        }
    }
}