using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Runtime.CompilerServices;
using Client.Utils.FireBase;
using Firebase.Crashlytics;
using Debug = UnityEngine.Debug;

namespace Client.Utils.GameLogger
{
    public static class GameLogger
    {
        public const string LOGGER_DIRECTIVE = "GAME_LOGGER";

        private static readonly List<int> LoggedExceptions = new();

        // ReSharper disable Unity.PerformanceAnalysis
        [MethodImpl (MethodImplOptions.AggressiveInlining)]
        public static void Log(string s)
        {
            DebugLog(s);
            FirebaseLog(s);
        }

        // ReSharper disable Unity.PerformanceAnalysis
        [Conditional(LOGGER_DIRECTIVE)]
        public static void Log(object obj) => Debug.Log(obj);

        // ReSharper disable Unity.PerformanceAnalysis
        [Conditional(LOGGER_DIRECTIVE)]
        public static void Log(string s, LogColor color) => Debug.Log(GetColoredMessage(s, color));

        // ReSharper disable Unity.PerformanceAnalysis
        [Conditional(LOGGER_DIRECTIVE)]
        public static void Log(object obj, LogColor color) => Debug.Log(GetColoredMessage(obj, color));

        // ReSharper disable Unity.PerformanceAnalysis
        [MethodImpl (MethodImplOptions.AggressiveInlining)]
        public static void LogWarning(string s)
        {
            DebugLogWarning(s);
            FirebaseLogWarning(s);
        }
        
        // ReSharper disable Unity.PerformanceAnalysis
        [Conditional(LOGGER_DIRECTIVE)]
        public static void LogWarning(object obj) => Debug.LogWarning(obj);

        // ReSharper disable Unity.PerformanceAnalysis
        [MethodImpl (MethodImplOptions.AggressiveInlining)]
        public static void LogError(string s)
        {
            DebugLogError(s);
            FirebaseLogError(s);
        }

        //Obsolete!
        //Use LogError(string s) instead
        public static void LogErrorException(string s, string id)
        {
            LogError(s);
        }

        // ReSharper disable Unity.PerformanceAnalysis
        [Conditional(LOGGER_DIRECTIVE)]
        public static void LogError(object obj) => Debug.LogError(obj);

        // ReSharper disable Unity.PerformanceAnalysis
        [Conditional(LOGGER_DIRECTIVE)]
        public static void LogError(string s, LogColor color) => Debug.LogError(GetColoredMessage(s, color));

        // ReSharper disable Unity.PerformanceAnalysis
        [Conditional(LOGGER_DIRECTIVE)]
        public static void LogError(object obj, LogColor color) => Debug.LogError(GetColoredMessage(obj, color));

        private static string GetString(object message)
        {
            if (message == null)
            {
                return "Null";
            }

            return message is IFormattable formattable ? formattable.ToString(null, CultureInfo.InvariantCulture) : message.ToString();
        }

        private static string GetColoredMessage(string s, LogColor color) => $"<color={GetColor(color)}>{s}</color>";

        private static string GetColoredMessage(object obj, LogColor color) => $"<color={GetColor(color)}>{GetString(obj)}</color>";

        private static string GetColor(LogColor color) => color.ToString().ToLower();

        [Conditional(LOGGER_DIRECTIVE)]
        private static void DebugLog(string s)
        {
            Debug.Log(GetMessageWithTime(s));
        }

        [MethodImpl (MethodImplOptions.AggressiveInlining)]
        private static void FirebaseLog(string s)
        {
#if !UNITY_EDITOR
            Crashlytics.Log($"Log {s}");
#endif
        }

        [Conditional(LOGGER_DIRECTIVE)]
        private static void DebugLogWarning(string s)
        {
            Debug.LogWarning(GetMessageWithTime(s));
        }

        private static void FirebaseLogWarning(string s)
        {
#if !UNITY_EDITOR
            Crashlytics.Log($"LogWarning {s}");
#endif
        }

        [Conditional(LOGGER_DIRECTIVE)]
        private static void DebugLogError(string s)
        {
            Debug.LogError(GetMessageWithTime(s));
        }

        [MethodImpl (MethodImplOptions.AggressiveInlining)]
        private static void FirebaseLogError(string s)
        {
#if !UNITY_EDITOR
            try
            {
                throw new CrashlyticsException(s);
            }
            catch (CrashlyticsException e)
            {
                int hashCode = e.GetHashCode();
                if (!LoggedExceptions.Contains(hashCode))
                {
                    Crashlytics.LogException(e);
                    LoggedExceptions.Add(hashCode);
                }
            }
#endif
        }

        private static string GetMessageWithTime(string s)
        {
#if !UNITY_EDITOR
            return $"[{DateTime.Now:HH:mm:ss}] {s}";
#else
            return s;
#endif
        }
    }
}