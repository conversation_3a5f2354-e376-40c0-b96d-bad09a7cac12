using System;
using System.Runtime.CompilerServices;
using Client.Utils.ResultTool.Errors;
using Newtonsoft.Json;

namespace Client.Utils.ResultTool.Results
{
    public class Result : IResult
    {
        protected Result(bool isSuccess, Error error = null)
        {
            if ((isSuccess && error != null) ||
                (!isSuccess && error == null))
            {
                throw new InvalidOperationException();
            }

            IsSuccess = isSuccess;
            Error = error;
        }

        public bool IsSuccess { get; }
        public bool IsFailure => !IsSuccess;

        public Error Error { get; }

        public static Result Success()
        {
            return new Result(true);
        }

        public static Result<TValue> Success<TValue>(TValue value)
        {
            return new Result<TValue>(value: value, true);
        }

        public static Result Failure(Error error)
        {
            return new Result(isSuccess: false, error);
        }
        
        public static Result Failure(string error)
        {
            return new Result(isSuccess: false, new Error(error));
        }

        public static Result<TValue> Failure<TValue>(Error error)
        {
            return new Result<TValue>(value: default, isSuccess: false, error);
        }
        
        public static Result<TValue> Failure<TValue>(string error)
        {
            return new Result<TValue>(value: default, isSuccess: false, new Error(error));
        }
        
        [MethodImpl (MethodImplOptions.AggressiveInlining)]
        public Result PrintLog()
        {
            return PrintLog(GetLog());
        }

        [MethodImpl (MethodImplOptions.AggressiveInlining)]
        public Result PrintLog(string message)
        {
            if (IsFailure)
            {
                GameLogger.GameLogger.LogError(message);
            }

            return this;
        }

        public virtual string GetLog()
        {
            return IsSuccess 
                ? "Result Sucess" 
                : $"Result Failed with error: {Error}";
        }
    }

    public class Result<TValue> : Result
    {
        private readonly TValue _value;

        public Result(TValue value, bool isSuccess, Error error = null) : base(isSuccess, error)
        {
            _value = value;
        }

        public TValue Value
            => IsSuccess
                ? _value
                : throw new InvalidOperationException("The value of a failed result can not be accessed.");

        [MethodImpl (MethodImplOptions.AggressiveInlining)]
        public new Result<TValue> PrintLog()
        {
            return PrintLog(GetLog());
        }

        [MethodImpl (MethodImplOptions.AggressiveInlining)]
        public new Result<TValue> PrintLog(string message)
        {
            if (IsSuccess)
            {
                GameLogger.GameLogger.Log(message);
            }
            else
            {
                GameLogger.GameLogger.LogError(message);
            }

            return this;
        }

        public override string GetLog()
        {
            return IsSuccess 
                ? $"[{typeof(TValue).Name}] Success. Value: {JsonConvert.SerializeObject(Value)}" 
                : $"[{typeof(TValue).Name}] Failed. Error: {Error}";
        }
    }
}