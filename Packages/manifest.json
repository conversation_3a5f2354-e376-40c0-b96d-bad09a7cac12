{"dependencies": {"com.chenpipi.project-pin-board": "https://github.com/ichenpipi/unity-project-pin-board.git", "com.coffee.softmask-for-ugui": "https://github.com/mob-sakai/SoftMaskForUGUI.git#1.0.2", "com.coffee.ui-particle": "https://github.com/mob-sakai/ParticleEffectForUGUI.git", "com.cysharp.unitask": "https://github.com/Cysharp/UniTask.git?path=src/UniTask/Assets/Plugins/UniTask", "com.dbrizov.naughtyattributes": "https://github.com/dbrizov/NaughtyAttributes.git#upm", "com.github-glitchenzo.nugetforunity": "https://github.com/GlitchEnzo/NuGetForUnity.git?path=/src/NuGetForUnity", "com.marijnzwemmer.unity-toolbar-extender": "https://github.com/marijnz/unity-toolbar-extender.git", "com.unity.2d.sprite": "1.0.0", "com.unity.addressables": "1.19.19", "com.unity.ads.ios-support": "1.0.0", "com.unity.analytics": "3.6.12", "com.unity.ide.cursor": "https://github.com/boxqkrtm/com.unity.ide.cursor.git", "com.unity.ide.rider": "3.0.36", "com.unity.ide.visualstudio": "2.0.22", "com.unity.ide.vscode": "1.2.5", "com.unity.mobile.android-logcat": "1.4.2", "com.unity.mobile.notifications": "2.3.2", "com.unity.polybrush": "1.1.2", "com.unity.purchasing": "5.0.0-pre.7", "com.unity.recorder": "3.0.4", "com.unity.shadergraph": "12.1.15", "com.unity.test-framework": "1.4.3", "com.unity.textmeshpro": "3.0.6", "com.unity.timeline": "1.6.5", "com.unity.toolchain.macos-x86_64-linux-x86_64": "2.0.4", "com.unity.toolchain.win-x86_64-linux-x86_64": "2.0.4", "com.unity.ugui": "1.0.0", "com.yasirkula.assetusagedetector": "https://github.com/yasirkula/UnityAssetUsageDetector.git", "com.yasirkula.beziersolution": "https://github.com/yasirkula/UnityBezierSolution.git", "jp.hadashikick.vcontainer": "https://github.com/hadashiA/VContainer.git?path=VContainer/Assets/VContainer#1.6.3", "com.unity.modules.ai": "1.0.0", "com.unity.modules.androidjni": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.cloth": "1.0.0", "com.unity.modules.director": "1.0.0", "com.unity.modules.imageconversion": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.particlesystem": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.physics2d": "1.0.0", "com.unity.modules.screencapture": "1.0.0", "com.unity.modules.terrain": "1.0.0", "com.unity.modules.terrainphysics": "1.0.0", "com.unity.modules.tilemap": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.uielements": "1.0.0", "com.unity.modules.umbra": "1.0.0", "com.unity.modules.unityanalytics": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.unitywebrequesttexture": "1.0.0", "com.unity.modules.unitywebrequestwww": "1.0.0", "com.unity.modules.vehicles": "1.0.0", "com.unity.modules.video": "1.0.0", "com.unity.modules.vr": "1.0.0", "com.unity.modules.wind": "1.0.0", "com.unity.modules.xr": "1.0.0"}, "scopedRegistries": [{"name": "jille<PERSON><PERSON>", "url": "https://npm.cloudsmith.io/jillejr/newtonsoft-json-for-unity", "scopes": ["jillejr.newtonsoft.json-for-unity"]}]}