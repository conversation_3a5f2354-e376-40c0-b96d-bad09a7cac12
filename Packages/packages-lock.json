{"dependencies": {"com.chenpipi.project-pin-board": {"version": "https://github.com/ichenpipi/unity-project-pin-board.git", "depth": 0, "source": "git", "dependencies": {}, "hash": "2ecf5f15398dcd085ebf8cb1131b973da3277559"}, "com.coffee.softmask-for-ugui": {"version": "https://github.com/mob-sakai/SoftMaskForUGUI.git#1.0.2", "depth": 0, "source": "git", "dependencies": {}, "hash": "b870d64b5704a764fd03aaccb11f2dbe652b1756"}, "com.coffee.ui-particle": {"version": "https://github.com/mob-sakai/ParticleEffectForUGUI.git", "depth": 0, "source": "git", "dependencies": {}, "hash": "4edcef1bbf49519ed2b841e669adb82fdc6a3d9b"}, "com.cysharp.unitask": {"version": "https://github.com/Cysharp/UniTask.git?path=src/UniTask/Assets/Plugins/UniTask", "depth": 0, "source": "git", "dependencies": {}, "hash": "50a67d8f419b6cea0398eff6c19cf0bbe11ed609"}, "com.dbrizov.naughtyattributes": {"version": "https://github.com/dbrizov/NaughtyAttributes.git#upm", "depth": 0, "source": "git", "dependencies": {}, "hash": "bf09948bcab85f0d1c30ad438e7f6d1d590823fb"}, "com.github-glitchenzo.nugetforunity": {"version": "https://github.com/GlitchEnzo/NuGetForUnity.git?path=/src/NuGetForUnity", "depth": 0, "source": "git", "dependencies": {}, "hash": "47ee976d56f867cb257ae5723985e40dc7d75aa0"}, "com.marijnzwemmer.unity-toolbar-extender": {"version": "https://github.com/marijnz/unity-toolbar-extender.git", "depth": 0, "source": "git", "dependencies": {}, "hash": "dbd76ed996483d219c39d240f785b1790aa039ed"}, "com.unity.2d.sprite": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.addressables": {"version": "1.19.19", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.imageconversion": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.scriptablebuildpipeline": "1.19.6", "com.unity.modules.unitywebrequestassetbundle": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.ads.ios-support": {"version": "1.0.0", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.analytics": {"version": "3.6.12", "depth": 0, "source": "registry", "dependencies": {"com.unity.ugui": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.ext.nunit": {"version": "2.0.3", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.ide.cursor": {"version": "https://github.com/boxqkrtm/com.unity.ide.cursor.git", "depth": 0, "source": "git", "dependencies": {"com.unity.test-framework": "1.1.9"}, "hash": "2c0153a9bab1abc783e5599fa2d2cc3c69d5e389"}, "com.unity.ide.rider": {"version": "3.0.36", "depth": 0, "source": "registry", "dependencies": {"com.unity.ext.nunit": "1.0.6"}, "url": "https://packages.unity.com"}, "com.unity.ide.visualstudio": {"version": "2.0.22", "depth": 0, "source": "registry", "dependencies": {"com.unity.test-framework": "1.1.9"}, "url": "https://packages.unity.com"}, "com.unity.ide.vscode": {"version": "1.2.5", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.mobile.android-logcat": {"version": "1.4.2", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.mobile.notifications": {"version": "file:com.unity.mobile.notifications@2.3.1", "depth": 0, "source": "embedded", "dependencies": {"com.unity.modules.androidjni": "1.0.0"}}, "com.unity.nuget.newtonsoft-json": {"version": "3.2.1", "depth": 2, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.polybrush": {"version": "1.1.2", "depth": 0, "source": "registry", "dependencies": {"com.unity.settings-manager": "1.0.3"}, "url": "https://packages.unity.com"}, "com.unity.purchasing": {"version": "5.0.0-pre.7", "depth": 0, "source": "registry", "dependencies": {"com.unity.ugui": "1.0.0", "com.unity.services.core": "1.12.5", "com.unity.modules.androidjni": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.recorder": {"version": "3.0.4", "depth": 0, "source": "registry", "dependencies": {"com.unity.timeline": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.render-pipelines.core": {"version": "12.1.15", "depth": 1, "source": "builtin", "dependencies": {"com.unity.ugui": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.scriptablebuildpipeline": {"version": "1.20.1", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.searcher": {"version": "4.9.1", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.services.core": {"version": "1.12.5", "depth": 1, "source": "registry", "dependencies": {"com.unity.modules.androidjni": "1.0.0", "com.unity.nuget.newtonsoft-json": "3.2.1", "com.unity.modules.unitywebrequest": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.settings-manager": {"version": "1.0.3", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.shadergraph": {"version": "12.1.15", "depth": 0, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "12.1.15", "com.unity.searcher": "4.9.1"}}, "com.unity.sysroot": {"version": "2.0.5", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.sysroot.linux-x86_64": {"version": "2.0.4", "depth": 1, "source": "registry", "dependencies": {"com.unity.sysroot": "2.0.5"}, "url": "https://packages.unity.com"}, "com.unity.test-framework": {"version": "1.4.3", "depth": 0, "source": "registry", "dependencies": {"com.unity.ext.nunit": "2.0.3", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.textmeshpro": {"version": "3.0.6", "depth": 0, "source": "registry", "dependencies": {"com.unity.ugui": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.timeline": {"version": "1.6.5", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.director": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.particlesystem": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.toolchain.macos-x86_64-linux-x86_64": {"version": "2.0.4", "depth": 0, "source": "registry", "dependencies": {"com.unity.sysroot": "2.0.5", "com.unity.sysroot.linux-x86_64": "2.0.4"}, "url": "https://packages.unity.com"}, "com.unity.toolchain.win-x86_64-linux-x86_64": {"version": "2.0.4", "depth": 0, "source": "registry", "dependencies": {"com.unity.sysroot": "2.0.5", "com.unity.sysroot.linux-x86_64": "2.0.4"}, "url": "https://packages.unity.com"}, "com.unity.ugui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0"}}, "com.yasirkula.assetusagedetector": {"version": "https://github.com/yasirkula/UnityAssetUsageDetector.git", "depth": 0, "source": "git", "dependencies": {}, "hash": "d57a707678b9deaeb1cf12f4cb66178554a35fa2"}, "com.yasirkula.beziersolution": {"version": "https://github.com/yasirkula/UnityBezierSolution.git", "depth": 0, "source": "git", "dependencies": {}, "hash": "79f4f28a8d4e3aea02f0600c6081a328700269ca"}, "jp.hadashikick.vcontainer": {"version": "https://github.com/hadashiA/VContainer.git?path=VContainer/Assets/VContainer#1.6.3", "depth": 0, "source": "git", "dependencies": {}, "hash": "afaeff82040994f7836afa9f5116eb52a4e54f69"}, "com.unity.modules.ai": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.androidjni": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.animation": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.assetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.audio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.cloth": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.director": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.animation": "1.0.0"}}, "com.unity.modules.imageconversion": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.imgui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.jsonserialize": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.particlesystem": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics2d": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.screencapture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.subsystems": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.terrain": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.terrainphysics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.terrain": "1.0.0"}}, "com.unity.modules.tilemap": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics2d": "1.0.0"}}, "com.unity.modules.ui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.uielements": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.uielementsnative": "1.0.0"}}, "com.unity.modules.uielementsnative": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.umbra": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unityanalytics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.unitywebrequest": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unitywebrequestassetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.unitywebrequestaudio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.audio": "1.0.0"}}, "com.unity.modules.unitywebrequesttexture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.unitywebrequestwww": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.vehicles": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.video": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.vr": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.xr": "1.0.0"}}, "com.unity.modules.wind": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.xr": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.subsystems": "1.0.0"}}}}